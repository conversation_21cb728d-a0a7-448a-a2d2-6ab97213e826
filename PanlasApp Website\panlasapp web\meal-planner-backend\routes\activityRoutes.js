const express = require('express');
const router = express.Router();
const activityController = require('../controllers/activityController');
const auth = require('../middleware/auth');

// Simple test route to verify routes are working
router.get('/ping', (req, res) => {
  res.json({
    message: 'Activity routes are working!',
    timestamp: new Date().toISOString(),
    route: '/api/activity/ping'
  });
});

// Log user activity
router.post('/log', auth, activityController.logActivity);

// Get activity log with pagination and filtering (admin only)
router.get('/log', auth, activityController.getActivityLog);

// Get activity for a specific user (admin only)
router.get('/user/:userId', auth, activityController.getUserActivity);

// Get recent activity for all users (admin only)
router.get('/recent', auth, activityController.getRecentActivity);

// Test endpoint to create sample activity (for debugging)
router.post('/test', auth, activityController.createTestActivity);

// Test endpoint without auth (for debugging)
router.post('/test-simple', (req, res) => {
  res.json({
    message: 'Test endpoint working!',
    timestamp: new Date().toISOString(),
    body: req.body,
    headers: {
      'user-agent': req.headers['user-agent'],
      'authorization': req.headers['authorization'] ? 'Present' : 'Missing'
    }
  });
});

// Database status check endpoint
router.get('/status', auth, activityController.checkDatabase);

// Manual activity creation endpoint (for testing)
router.post('/manual', auth, activityController.createManualActivity);

module.exports = router;
