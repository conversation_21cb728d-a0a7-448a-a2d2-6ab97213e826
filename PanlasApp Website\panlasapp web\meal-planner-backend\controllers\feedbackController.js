const Feedback = require('../models/Feedback');
const User = require('../models/User');

// Submit feedback (authenticated users)
exports.submitFeedback = async (req, res) => {
  console.log('=== SUBMIT FEEDBACK ENDPOINT CALLED ===');
  console.log('Request body:', req.body);
  console.log('User:', req.user);
  
  try {
    const { subject, message, category, rating } = req.body;
    
    if (!req.user) {
      console.log('ERROR: No user found in request');
      return res.status(401).json({ message: 'Authentication required' });
    }
    
    const userId = req.user.id;
    console.log('User ID:', userId);

    // Validate required fields
    if (!subject || !message || !category) {
      console.log('ERROR: Missing required fields');
      return res.status(400).json({
        message: 'Subject, message, and category are required'
      });
    }

    // Create feedback
    console.log('Creating feedback...');
    const feedback = new Feedback({
      user: userId,
      subject,
      message,
      category,
      rating
    });

    await feedback.save();
    console.log('Feedback saved successfully');

    // Populate user information for response
    await feedback.populate('user', 'username email');

    res.status(201).json({
      message: 'Feedback submitted successfully',
      feedback: {
        id: feedback._id,
        subject: feedback.subject,
        message: feedback.message,
        category: feedback.category,
        status: feedback.status,
        createdAt: feedback.createdAt,
        user: feedback.user
      }
    });
    console.log('Response sent successfully');
  } catch (error) {
    console.error('Submit feedback error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

// Get all feedback (admin or sub admin with feedback management permission)
exports.getAllFeedback = async (req, res) => {
  console.log('=== GET ALL FEEDBACK ENDPOINT CALLED ===');
  try {
    // Permission check is handled by middleware
    // if (!req.user.isAdmin) {
    //   return res.status(403).json({ message: 'Access denied. Admin only.' });
    // }

    const { 
      page = 1, 
      limit = 10, 
      category, 
      status, 
      priority,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;

    // Build filter object
    const filter = {};
    if (category && category !== 'all') filter.category = category;
    if (status && status !== 'all') filter.status = status;
    if (priority && priority !== 'all') filter.priority = priority;

    // Build sort object
    const sort = {};
    sort[sortBy] = sortOrder === 'desc' ? -1 : 1;

    // Calculate pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);

    // Get feedback with pagination
    const feedback = await Feedback.find(filter)
      .populate('user', 'username email firstName lastName')
      .populate('adminResponse.respondedBy', 'username email')
      .sort(sort)
      .skip(skip)
      .limit(parseInt(limit));

    // Get total count for pagination
    const totalCount = await Feedback.countDocuments(filter);
    const totalPages = Math.ceil(totalCount / parseInt(limit));

    // Get feedback statistics
    const stats = await Feedback.aggregate([
      {
        $group: {
          _id: null,
          total: { $sum: 1 },
          open: { $sum: { $cond: [{ $eq: ['$status', 'open'] }, 1, 0] } },
          inProgress: { $sum: { $cond: [{ $eq: ['$status', 'in_progress'] }, 1, 0] } },
          resolved: { $sum: { $cond: [{ $eq: ['$status', 'resolved'] }, 1, 0] } },
          closed: { $sum: { $cond: [{ $eq: ['$status', 'closed'] }, 1, 0] } },
          avgRating: { $avg: '$rating' }
        }
      }
    ]);

    res.json({
      feedback,
      pagination: {
        currentPage: parseInt(page),
        totalPages,
        totalCount,
        hasNext: parseInt(page) < totalPages,
        hasPrev: parseInt(page) > 1
      },
      stats: stats[0] || {
        total: 0,
        open: 0,
        inProgress: 0,
        resolved: 0,
        closed: 0,
        avgRating: 0
      }
    });
  } catch (error) {
    console.error('Get all feedback error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

// Update feedback status (admin or sub admin with feedback management permission)
exports.updateFeedbackStatus = async (req, res) => {
  try {
    // Permission check is handled by middleware
    // if (!req.user.isAdmin) {
    //   return res.status(403).json({ message: 'Access denied. Admin only.' });
    // }

    const { feedbackId } = req.params;
    const { status, priority } = req.body;

    const updateData = {};
    if (status) updateData.status = status;
    if (priority) updateData.priority = priority;

    const feedback = await Feedback.findByIdAndUpdate(
      feedbackId,
      updateData,
      { new: true, runValidators: true }
    ).populate('user', 'username email');

    if (!feedback) {
      return res.status(404).json({ message: 'Feedback not found' });
    }

    res.json({
      message: 'Feedback updated successfully',
      feedback
    });
  } catch (error) {
    console.error('Update feedback status error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

// Add admin response to feedback (admin or sub admin with feedback management permission)
exports.addAdminResponse = async (req, res) => {
  try {
    // Permission check is handled by middleware
    // if (!req.user.isAdmin) {
    //   return res.status(403).json({ message: 'Access denied. Admin only.' });
    // }

    const { feedbackId } = req.params;
    const { message } = req.body;

    if (!message) {
      return res.status(400).json({ message: 'Response message is required' });
    }

    const feedback = await Feedback.findByIdAndUpdate(
      feedbackId,
      {
        adminResponse: {
          message,
          respondedBy: req.user.id,
          respondedAt: new Date()
        },
        status: 'in_progress'
      },
      { new: true, runValidators: true }
    ).populate('user', 'username email')
     .populate('adminResponse.respondedBy', 'username email');

    if (!feedback) {
      return res.status(404).json({ message: 'Feedback not found' });
    }

    res.json({
      message: 'Admin response added successfully',
      feedback
    });
  } catch (error) {
    console.error('Add admin response error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

// Delete feedback (admin or sub admin with feedback management permission)
exports.deleteFeedback = async (req, res) => {
  try {
    // Permission check is handled by middleware
    // if (!req.user.isAdmin) {
    //   return res.status(403).json({ message: 'Access denied. Admin only.' });
    // }

    const { feedbackId } = req.params;

    const feedback = await Feedback.findByIdAndDelete(feedbackId);

    if (!feedback) {
      return res.status(404).json({ message: 'Feedback not found' });
    }

    res.json({
      message: 'Feedback deleted successfully',
      deletedFeedback: {
        id: feedback._id,
        subject: feedback.subject
      }
    });
  } catch (error) {
    console.error('Delete feedback error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};
