# Automatic Meal Plan Updates Feature

## Overview

This feature automatically updates future meal plans when a user's dietary preferences change. When dietary restrictions, allergies, or disliked ingredients are modified, the system:

1. **Detects conflicts** in existing future meal plans
2. **Uses AI** to suggest appropriate replacement meals
3. **Automatically updates** meal plans with suitable alternatives
4. **Notifies users** about the changes made

## Architecture

### Core Components

#### 1. Meal Plan Update Service (`services/mealPlanUpdateService.js`)
- **Main Function**: `updateFutureMealPlansForDietaryChanges(userId, newDietaryPreferences)`
- **Conflict Detection**: `checkMealConflict(meal, dietaryPreferences)`
- **AI Integration**: `getAIReplacementMeal(originalMeal, mealType, dietaryPreferences)`
- **Meal Filtering**: `getFilteredMeals(mealType, dietaryPreferences)`

#### 2. Enhanced Gemini AI Service (`services/geminiService.js`)
- **New Method**: `generateMealReplacement(originalMeal, familyProfile, availableMeals, mealType)`
- Provides targeted meal replacement suggestions with reasoning

#### 3. Updated User Controller (`controllers/userController.js`)
- **Enhanced**: `updateDietaryPreferences()` endpoint
- **New Helper**: `hasSignificantDietaryChanges()` function
- Automatically triggers meal plan updates when preferences change significantly

#### 4. New Meal Plan Endpoints (`controllers/mealPlanController.js`)
- `updateMealPlansForDietaryChanges()` - Manual trigger for updates
- `checkDietaryConflicts()` - Check for conflicts without updating
- `resolveDietaryConflicts()` - Resolve specific conflicts

### Mobile App Integration

#### 5. Enhanced Dietary Preferences Screen (`panlas-mobile-app/src/screens/main/DietaryPreferencesScreen.js`)
- Shows meal plan update notifications
- Provides feedback on automatic changes
- Links to view updated meal plans

#### 6. Updated API Service (`panlas-mobile-app/src/services/api.js`)
- New endpoints for meal plan conflict management
- Enhanced dietary preferences update handling

## How It Works

### 1. Dietary Preference Change Detection

When a user updates their dietary preferences, the system compares old vs new preferences:

```javascript
function hasSignificantDietaryChanges(oldPrefs, newPrefs) {
  // Checks for changes in:
  // - Dietary restrictions (Vegetarian, Vegan, Gluten-Free, etc.)
  // - Allergies (Nuts, Dairy, etc.)
  // - Disliked ingredients
}
```

### 2. Future Meal Plan Identification

The system finds all meal plans from today onwards:

```javascript
const futureMealPlans = await MealPlan.find({
  user: userId,
  date: { $gte: today },
  isTemplate: false
});
```

### 3. Conflict Detection

For each meal in future plans, the system checks:

- **Dietary Restrictions**: Does the meal meet the new restrictions?
- **Allergies**: Does the meal contain any new allergens?
- **Disliked Ingredients**: Does the meal contain newly disliked ingredients?

### 4. AI-Powered Meal Replacement

When conflicts are found, the AI system:

1. **Filters available meals** based on new dietary preferences
2. **Analyzes the original meal** (nutrition, type, characteristics)
3. **Suggests the best replacement** with detailed reasoning
4. **Provides alternatives** if the primary suggestion isn't suitable

### 5. Automatic Updates

The system:

1. **Replaces conflicting meals** with AI suggestions
2. **Preserves meal plan structure** (breakfast, lunch, dinner, snacks)
3. **Maintains nutritional balance** where possible
4. **Updates timestamps** to track changes

## API Endpoints

### Automatic Updates (Triggered by Preference Changes)

```
PUT /users/dietary-preferences
```

**Response includes meal plan update information:**
```json
{
  "success": true,
  "dietaryPreferences": { ... },
  "mealPlanUpdates": {
    "success": true,
    "plansChecked": 7,
    "plansUpdated": 3,
    "mealsReplaced": 8,
    "updateDetails": [...]
  }
}
```

### Manual Updates

```
POST /meal-plans/update-for-dietary-changes
Body: { "dietaryPreferences": { ... } }
```

### Conflict Checking

```
GET /meal-plans/dietary-conflicts?dietaryPreferences={...}
```

### Conflict Resolution

```
POST /meal-plans/resolve-conflicts
Body: { "conflicts": [...], "dietaryPreferences": { ... } }
```

## User Experience

### Mobile App Flow

1. **User updates dietary preferences** in the app
2. **System processes changes** automatically in the background
3. **User receives notification** about meal plan updates
4. **Detailed feedback** shows what was changed and why
5. **Option to view** updated meal plans immediately

### Notification Examples

**Successful Updates:**
> "Your dietary preferences have been saved!
> 
> 🍽️ Meal Plan Updates:
> • 3 meal plans updated
> • 8 meals replaced with AI suggestions
> 
> Your future meal plans now match your new dietary preferences!"

**No Changes Needed:**
> "Your dietary preferences have been saved!
> 
> No changes were needed to your existing meal plans."

## Configuration

### Dietary Restrictions Supported

- Vegetarian
- Vegan
- Gluten-Free
- Dairy-Free
- Nut-Free
- Low-Carb
- Keto
- Pescatarian
- Halal
- Paleo

### Conflict Detection Logic

**Restrictions (Inclusive)**: User wants ONLY these types of food
**Allergies (Exclusive)**: User does NOT want these ingredients
**Disliked Ingredients (Exclusive)**: User prefers to avoid these

### AI Replacement Criteria

1. **Dietary Compliance**: Must meet all new restrictions and avoid allergies
2. **Nutritional Similarity**: Similar calorie and macro content when possible
3. **Meal Type Appropriateness**: Breakfast meals for breakfast, etc.
4. **Availability**: Must exist in the database
5. **User Preferences**: Considers family profile and goals

## Testing

### Test Script

Run the comprehensive test:

```bash
node scripts/testMealPlanUpdates.js
```

### Manual Testing Steps

1. Create a user with basic dietary preferences
2. Create meal plans for the next week
3. Update dietary preferences to add restrictions/allergies
4. Verify that conflicting meals are automatically replaced
5. Check that AI suggestions are appropriate and meet new criteria

## Error Handling

- **No suitable replacements found**: Logs warning, keeps original meal
- **AI service unavailable**: Falls back to first suitable meal from filtered list
- **Database errors**: Logs error, doesn't fail preference update
- **Invalid preferences**: Validates input before processing

## Performance Considerations

- **Batch processing**: Updates multiple meal plans efficiently
- **Meal filtering**: Limits database queries with targeted filters
- **AI rate limiting**: Handles API limits gracefully
- **Background processing**: Doesn't block user interface

## Future Enhancements

1. **User preferences for replacement style** (similar nutrition vs. variety)
2. **Undo functionality** for automatic changes
3. **Scheduling updates** for specific dates
4. **Family member conflict resolution**
5. **Nutritionist review** for complex dietary changes
