const mongoose = require('mongoose');
const User = require('./models/User');

// Connect to MongoDB - Use the same connection as the server
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb+srv://johnmatthewmartin09:<EMAIL>/mealplanner?retryWrites=true&w=majority&appName=MealPlannersCluster';
mongoose.connect(MONGODB_URI);

async function findAkUser() {
  try {
    console.log('🔍 Looking for user with family member named "Ak"...');
    
    // Find user with family member named "Ak"
    const user = await User.findOne({ 
      'familyMembers.name': { $regex: /^Ak$/i }
    });
    
    if (!user) {
      console.log('❌ No user found with family member named "Ak"');
      
      // Let's check all family member names
      console.log('\n📋 All family member names in database:');
      const allUsers = await User.find({ 'familyMembers.0': { $exists: true } });
      allUsers.forEach(user => {
        console.log(`User: ${user.email}`);
        user.familyMembers.forEach(member => {
          console.log(`  - ${member.name}: disliked = ${JSON.stringify(member.dietaryPreferences?.dislikedIngredients)}`);
        });
      });
      return;
    }
    
    console.log('✅ Found user:', user.email);
    
    // Find the "Ak" family member
    const akMember = user.familyMembers.find(member => 
      member.name.toLowerCase() === 'ak'
    );
    
    if (akMember) {
      console.log('👤 Found "Ak" family member:');
      console.log('  - ID:', akMember._id);
      console.log('  - Name:', akMember.name);
      console.log('  - Date of Birth:', akMember.dateOfBirth);
      console.log('  - Dietary Preferences:', JSON.stringify(akMember.dietaryPreferences, null, 2));
      console.log('  - Disliked Ingredients:', akMember.dietaryPreferences?.dislikedIngredients);
      
      // Check if disliked ingredients is an array
      const dislikedIngredients = akMember.dietaryPreferences?.dislikedIngredients;
      console.log('  - Type of disliked ingredients:', typeof dislikedIngredients);
      console.log('  - Is array:', Array.isArray(dislikedIngredients));
      console.log('  - Length:', dislikedIngredients?.length);
      
      if (Array.isArray(dislikedIngredients) && dislikedIngredients.length > 0) {
        console.log('  - Individual ingredients:');
        dislikedIngredients.forEach((ingredient, index) => {
          console.log(`    ${index + 1}. "${ingredient}" (type: ${typeof ingredient})`);
        });
      }
    }
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    mongoose.connection.close();
  }
}

findAkUser();
