.meal-management {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

.meal-management h1 {
  color: #333;
  margin-bottom: 30px;
  font-size: 2rem;
}

/* Tabs */
.meal-tabs {
  display: flex;
  gap: 10px;
  margin-bottom: 30px;
  border-bottom: 2px solid #e0e0e0;
}

.tab-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: none;
  border: none;
  border-bottom: 3px solid transparent;
  cursor: pointer;
  font-size: 16px;
  color: #666;
  transition: all 0.3s ease;
}

.tab-button:hover {
  color: #20C5AF;
  background-color: #f8f9fa;
}

.tab-button.active {
  color: #20C5AF;
  border-bottom-color: #20C5AF;
  font-weight: 600;
}

/* Error Message */
.error-message {
  background-color: #f8d7da;
  color: #721c24;
  padding: 12px;
  border-radius: 6px;
  margin-bottom: 20px;
  border: 1px solid #f5c6cb;
}

/* Meals View */
.meals-view {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* Filters */
.meal-filters {
  padding: 20px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e0e0e0;
  display: flex;
  gap: 20px;
  align-items: center;
  flex-wrap: wrap;
}

.search-box {
  position: relative;
  flex: 1;
  min-width: 300px;
}

.search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #666;
}

.search-input {
  width: 100%;
  padding: 12px 12px 12px 40px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
}

.filter-controls {
  display: flex;
  gap: 15px;
}

.filter-select {
  padding: 10px 15px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  background: white;
  min-width: 150px;
}

/* Statistics */
.meals-stats {
  display: flex;
  gap: 20px;
  padding: 20px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e0e0e0;
}

.stat-card {
  background: white;
  padding: 20px;
  border-radius: 8px;
  text-align: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  flex: 1;
}

.stat-card h3 {
  margin: 0 0 10px 0;
  color: #666;
  font-size: 14px;
  font-weight: 500;
}

.stat-card p {
  margin: 0;
  font-size: 24px;
  font-weight: bold;
  color: black;
}

/* Table */
.meals-table-container {
  overflow-x: auto;
}

.meals-table {
  width: 100%;
  border-collapse: collapse;
  background: white;
}

.meals-table th,
.meals-table td {
  padding: 15px;
  text-align: left;
  border-bottom: 1px solid #e0e0e0;
}

.meals-table th {
  background-color: #f8f9fa;
  font-weight: 600;
  color: #333;
  position: sticky;
  top: 0;
  z-index: 10;
}

.meals-table tr:hover {
  background-color: #f8f9fa;
}

.meal-image {
  width: 60px;
  height: 60px;
  border-radius: 8px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f0f0f0;
}

.meal-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.no-image {
  font-size: 10px;
  color: #999;
  text-align: center;
}

.meal-name strong {
  display: block;
  color: #333;
  margin-bottom: 4px;
}

.meal-name small {
  color: #666;
  font-size: 12px;
}

.rating {
  display: flex;
  align-items: center;
  gap: 5px;
  color: #ffc107;
}

.rating span {
  font-size: 12px;
  color: #666;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.btn-view,
.btn-edit,
.btn-delete {
  padding: 8px 12px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
}

.btn-view {
  background-color: #007bff;
  color: white;
}

.btn-view:hover {
  background-color: #0056b3;
}

.btn-edit {
  background-color: #007bff;
  color: white;
}

.btn-edit:hover {
  background-color: #0056b3;
}

.btn-delete {
  background-color: #dc3545;
  color: white;
}

.btn-delete:hover {
  background-color: #c82333;
}

.no-meals {
  text-align: center;
  padding: 40px;
  color: #666;
  font-style: italic;
}

/* Pagination */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 15px;
  padding: 20px;
  background-color: #f8f9fa;
}

.pagination-btn {
  padding: 8px 16px;
  border: 1px solid #ddd;
  background: white;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.pagination-btn:hover:not(:disabled) {
  background-color: #20C5AF;
  color: white;
  border-color: #20C5AF;
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-info {
  font-size: 14px;
  color: #666;
}

/* Loading */
.loading {
  text-align: center;
  padding: 40px;
  font-size: 18px;
  color: #666;
}

/* Meal Details View */
.meal-details-view {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.details-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 30px;
  background-color: #f8f9fa;
  border-bottom: 2px solid #e0e0e0;
}

.details-header h2 {
  margin: 0;
  color: #333;
  font-size: 1.5rem;
}

.btn-close {
  background: none;
  border: none;
  font-size: 20px;
  color: #666;
  cursor: pointer;
  padding: 5px;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.btn-close:hover {
  background-color: #e9ecef;
  color: #333;
}

.details-content {
  padding: 30px;
}

.details-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
  margin-bottom: 30px;
}

.detail-section {
  background-color: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
}

.detail-section.full-width {
  grid-column: 1 / -1;
}

.detail-section h3 {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 1.1rem;
  border-bottom: 2px solid #20C5AF;
  padding-bottom: 8px;
}

.detail-item {
  margin-bottom: 15px;
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.detail-item strong {
  color: #333;
  font-weight: 600;
}

.nutrition-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 15px;
}

.nutrition-item {
  background: white;
  padding: 10px;
  border-radius: 4px;
  border: 1px solid #ddd;
}

.tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 8px;
}

.tag {
  background-color: #20C5AF;
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.allergen-tag {
  background-color: #dc3545;
}

.diet-types {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 10px;
}

.diet-type {
  background-color: #28a745;
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.ingredients-list,
.instructions-list {
  margin: 0;
  padding-left: 20px;
}

.ingredients-list li,
.instructions-list li {
  margin-bottom: 8px;
  line-height: 1.5;
}

.meal-image-large {
  text-align: center;
}

.meal-image-large img {
  max-width: 100%;
  max-height: 400px;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.details-actions {
  display: flex;
  justify-content: center;
  gap: 15px;
  padding-top: 20px;
  border-top: 2px solid #e0e0e0;
}

/* Meal Form */
.meal-form-container {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.meal-form {
  padding: 30px;
}

.form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 2px solid #e0e0e0;
}

.form-header h2 {
  margin: 0;
  color: #333;
  font-size: 1.5rem;
}

.form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
  margin-bottom: 30px;
}

.form-section {
  background-color: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
}

.form-section.full-width {
  grid-column: 1 / -1;
}

.form-section h3 {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 1.1rem;
  border-bottom: 2px solid #20C5AF;
  padding-bottom: 8px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #333;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #20C5AF;
  box-shadow: 0 0 0 2px rgba(32, 197, 175, 0.2);
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
}

.checkbox-group {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 8px;
  margin-top: 8px;
}

.checkbox-label {
  display: flex;
  align-items: left;
  gap: 12px;
  font-weight: normal;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  transition: background-color 0.3s ease;
  user-select: none;
}

.checkbox-label:hover {
  background-color: rgba(32, 197, 175, 0.1);
}

/* Custom checkbox styling - single entity */
.meal-management .checkbox-label {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding: 12px 16px;
  margin-bottom: 8px;
  background: white;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  user-select: none;
  min-height: 50px;
}

.meal-management .checkbox-label:hover {
  background-color: rgba(32, 197, 175, 0.08);
  border-color: #20C5AF;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(32, 197, 175, 0.15);
}

.meal-management .checkbox-label input[type="checkbox"] {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}

.meal-management .checkbox-label::before {
  content: '';
  display: inline-block;
  width: 20px;
  height: 20px;
  margin-right: 12px;
  margin-left: 8px;
  margin-top: 8px;
  border: 2px solid #ddd;
  border-radius: 4px;
  background: white;
  transition: all 0.2s ease;
  flex-shrink: 0;
  align-self: center;
}

.meal-management .checkbox-label:hover::before {
  border-color: #20C5AF;
}

.meal-management .checkbox-label.checked::before {
  background-color: #20C5AF;
  border-color: #20C5AF;
  background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='m13.854 3.646-7.5 7.5a.5.5 0 0 1-.708 0l-3.5-3.5a.5.5 0 1 1 .708-.708L6 10.293l7.146-7.147a.5.5 0 0 1 .708.708z'/%3e%3c/svg%3e");
  background-size: 12px;
  background-position: center;
  background-repeat: no-repeat;
}

.meal-management .checkbox-label span {
  font-size: 14px;
  color: #333;
  font-weight: 500;
  line-height: 1.4;
  flex: 1;
  text-align: center;
  margin-right: 32px; /* Compensate for checkbox width to center text */
}

.meal-management .checkbox-label.checked {
  background-color: rgba(32, 197, 175, 0.1);
  border-color: #20C5AF;
}

.meal-management .checkbox-label.checked span {
  color: #20C5AF;
  font-weight: 600;
  flex: 1;
  text-align: center;
  margin-right: 32px; /* Compensate for checkbox width to center text */
}

.array-input {
  min-height: 120px;
  resize: vertical;
  font-family: monospace;
}

.form-group small {
  color: #666;
  font-size: 12px;
  margin-top: 5px;
  display: block;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 15px;
  padding-top: 20px;
  border-top: 2px solid #e0e0e0;
}

.btn-cancel,
.btn-save {
  padding: 12px 24px;
  border: none;
  border-radius: 6px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.btn-cancel {
  background-color: #6c757d;
  color: white;
}

.btn-cancel:hover {
  background-color: #5a6268;
}

.btn-save {
  background-color: #20C5AF;
  color: white;
}

.btn-save:hover:not(:disabled) {
  background-color: #1ba085;
}

.btn-save:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

/* Responsive Design */
@media (max-width: 768px) {
  .meal-filters {
    flex-direction: column;
    align-items: stretch;
  }

  .search-box {
    min-width: auto;
  }

  .filter-controls {
    flex-direction: column;
  }

  .meals-stats {
    flex-direction: column;
  }

  .meals-table {
    font-size: 14px;
  }

  .meals-table th,
  .meals-table td {
    padding: 10px 8px;
  }

  .action-buttons {
    flex-direction: column;
  }

  .form-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .checkbox-group {
    grid-template-columns: 1fr;
  }

  .form-actions {
    flex-direction: column;
  }

  .meal-form {
    padding: 20px;
  }

  .details-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .details-content {
    padding: 30px;
    align-items: right;
  }

  .nutrition-grid {
    grid-template-columns: 1fr;
  }
}
