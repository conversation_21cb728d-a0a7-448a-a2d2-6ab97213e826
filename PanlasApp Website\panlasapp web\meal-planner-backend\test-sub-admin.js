const axios = require('axios');

// Test configuration
const BASE_URL = 'http://localhost:5000/api';
let adminToken = '';
let subAdminToken = '';
let testUserId = '';

// Test credentials (you'll need to create these users first)
const adminCredentials = {
  email: '<EMAIL>',
  password: 'admin123'
};

const testUserCredentials = {
  email: '<EMAIL>',
  password: 'test123'
};

// Helper function to make API requests
async function makeRequest(method, url, data = null, token = '') {
  try {
    const config = {
      method,
      url: `${BASE_URL}${url}`,
      headers: {}
    };

    if (token) {
      config.headers['x-auth-token'] = token;
    }

    if (data) {
      config.headers['Content-Type'] = 'application/json';
      config.data = data;
    }

    const response = await axios(config);
    return { success: true, data: response.data, status: response.status };
  } catch (error) {
    return { 
      success: false, 
      error: error.response?.data?.message || error.message,
      status: error.response?.status || 500
    };
  }
}

// Test functions
async function loginAsAdmin() {
  console.log('🔐 Logging in as admin...');
  const result = await makeRequest('POST', '/users/login', adminCredentials);
  
  if (result.success) {
    adminToken = result.data.token;
    console.log('✅ Admin login successful');
    return true;
  } else {
    console.log('❌ Admin login failed:', result.error);
    return false;
  }
}

async function createTestUser() {
  console.log('👤 Creating test user...');
  const userData = {
    username: 'testuser',
    email: testUserCredentials.email,
    password: testUserCredentials.password,
    firstName: 'Test',
    lastName: 'User'
  };

  const result = await makeRequest('POST', '/admin/users', userData, adminToken);
  
  if (result.success) {
    testUserId = result.data.user._id;
    console.log('✅ Test user created:', testUserId);
    return true;
  } else {
    console.log('❌ Test user creation failed:', result.error);
    return false;
  }
}

async function makeUserSubAdmin() {
  console.log('🔧 Making user sub admin...');
  const result = await makeRequest('PUT', `/admin/users/${testUserId}/make-admin`, 
    { adminRole: 'sub_admin' }, adminToken);
  
  if (result.success) {
    console.log('✅ User promoted to sub admin');
    return true;
  } else {
    console.log('❌ Sub admin promotion failed:', result.error);
    return false;
  }
}

async function loginAsSubAdmin() {
  console.log('🔐 Logging in as sub admin...');
  const result = await makeRequest('POST', '/users/login', testUserCredentials);
  
  if (result.success) {
    subAdminToken = result.data.token;
    console.log('✅ Sub admin login successful');
    return true;
  } else {
    console.log('❌ Sub admin login failed:', result.error);
    return false;
  }
}

async function testMealManagement() {
  console.log('\n📋 Testing Meal Management Access...');
  
  // Test GET meals
  const getMeals = await makeRequest('GET', '/meals', null, subAdminToken);
  console.log(getMeals.success ? '✅ GET meals: SUCCESS' : '❌ GET meals: FAILED');
  
  // Test POST meal
  const createMeal = await makeRequest('POST', '/meals', {
    name: 'Test Sub Admin Meal',
    description: 'Created by sub admin test',
    category: ['test'],
    calories: 100
  }, subAdminToken);
  console.log(createMeal.success ? '✅ CREATE meal: SUCCESS' : '❌ CREATE meal: FAILED');
  
  // Test PUT meal (if create was successful)
  if (createMeal.success && createMeal.data._id) {
    const updateMeal = await makeRequest('PUT', `/meals/${createMeal.data._id}`, {
      name: 'Updated Test Meal'
    }, subAdminToken);
    console.log(updateMeal.success ? '✅ UPDATE meal: SUCCESS' : '❌ UPDATE meal: FAILED');
    
    // Test DELETE meal
    const deleteMeal = await makeRequest('DELETE', `/meals/${createMeal.data._id}`, null, subAdminToken);
    console.log(deleteMeal.success ? '✅ DELETE meal: SUCCESS' : '❌ DELETE meal: FAILED');
  }
}

async function testFeedbackManagement() {
  console.log('\n💬 Testing Feedback Management Access...');
  
  // Test GET all feedback
  const getFeedback = await makeRequest('GET', '/feedback/all', null, subAdminToken);
  console.log(getFeedback.success ? '✅ GET feedback: SUCCESS' : '❌ GET feedback: FAILED');
}

async function testRestrictedAccess() {
  console.log('\n🚫 Testing Restricted Access (should fail)...');
  
  // Test user management (should fail)
  const getUsers = await makeRequest('GET', '/admin/users', null, subAdminToken);
  console.log(!getUsers.success && getUsers.status === 403 ? 
    '✅ User management blocked: SUCCESS' : '❌ User management blocked: FAILED');
  
  // Test analytics (should fail)
  const getAnalytics = await makeRequest('GET', '/admin/analytics/geolocation', null, subAdminToken);
  console.log(!getAnalytics.success && getAnalytics.status === 403 ? 
    '✅ Analytics blocked: SUCCESS' : '❌ Analytics blocked: FAILED');
  
  // Test system overview (should fail)
  const getOverview = await makeRequest('GET', '/admin/overview', null, subAdminToken);
  console.log(!getOverview.success && getOverview.status === 403 ? 
    '✅ Overview blocked: SUCCESS' : '❌ Overview blocked: FAILED');
}

async function cleanup() {
  console.log('\n🧹 Cleaning up...');
  
  // Remove admin privileges from test user
  if (testUserId) {
    await makeRequest('PUT', `/admin/users/${testUserId}/remove-admin`, null, adminToken);
    console.log('✅ Admin privileges removed from test user');
  }
}

// Main test function
async function runTests() {
  console.log('🚀 Starting Sub Admin Functionality Tests\n');
  
  try {
    // Setup
    if (!await loginAsAdmin()) return;
    if (!await createTestUser()) return;
    if (!await makeUserSubAdmin()) return;
    if (!await loginAsSubAdmin()) return;
    
    // Run tests
    await testMealManagement();
    await testFeedbackManagement();
    await testRestrictedAccess();
    
    console.log('\n✅ All tests completed!');
    
  } catch (error) {
    console.error('❌ Test error:', error.message);
  } finally {
    await cleanup();
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  runTests();
}

module.exports = { runTests };
