require('dotenv').config();
const mongoose = require('mongoose');
const User = require('./models/User');

async function testUpdateDislikedIngredients() {
  try {
    console.log('🔍 Connecting to MongoDB...');
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ Connected to MongoDB');

    // Find a test user (you can replace this with your actual user ID)
    const testUserId = '685883ea3cc2df1d683b8714'; // Replace with your user ID
    
    console.log('\n🔍 Looking for user:', testUserId);
    const user = await User.findById(testUserId);
    
    if (!user) {
      console.log('❌ User not found');
      return;
    }

    console.log('✅ User found:', user.name);
    console.log('📋 Current family members:', user.familyMembers.length);

    // Find the family member named "Ak"
    const akMember = user.familyMembers.find(member => member.name === 'Ak');
    
    if (!akMember) {
      console.log('❌ Family member "Ak" not found');
      console.log('Available family members:', user.familyMembers.map(m => m.name));
      return;
    }

    console.log('\n✅ Found family member "Ak"');
    console.log('Current disliked ingredients:', akMember.dietaryPreferences?.dislikedIngredients);
    console.log('Current restrictions:', akMember.dietaryPreferences?.restrictions);
    console.log('Current allergies:', akMember.dietaryPreferences?.allergies);

    // Test updating disliked ingredients
    console.log('\n🧪 Testing update with "Elaichi" as disliked ingredient...');
    
    // Find the member index
    const memberIndex = user.familyMembers.findIndex(m => m._id.toString() === akMember._id.toString());
    
    if (memberIndex === -1) {
      console.log('❌ Could not find member index');
      return;
    }

    // Update the disliked ingredients
    const updateData = {
      name: 'Ak',
      dateOfBirth: akMember.dateOfBirth,
      dietaryPreferences: {
        restrictions: akMember.dietaryPreferences?.restrictions || [],
        allergies: akMember.dietaryPreferences?.allergies || [],
        dislikedIngredients: ['Elaichi'], // This should be saved
        calorieTarget: akMember.dietaryPreferences?.calorieTarget || null,
        macroTargets: {
          protein: null,
          carbs: null,
          fat: null
        },
        mealFrequency: akMember.dietaryPreferences?.mealFrequency || 3
      }
    };

    console.log('Update data:', JSON.stringify(updateData, null, 2));

    // Apply the update logic from the controller
    const currentMember = user.familyMembers[memberIndex];
    
    // Update basic info
    if (updateData.name) currentMember.name = updateData.name;
    if (updateData.dateOfBirth) currentMember.dateOfBirth = updateData.dateOfBirth;

    // Update dietary preferences using the new logic
    if (updateData.dietaryPreferences) {
      const macroTargets = updateData.dietaryPreferences.macroTargets ||
                          currentMember.dietaryPreferences?.macroTargets ||
                          {};

      const validMacroTargets = {
        protein: macroTargets.protein || null,
        carbs: macroTargets.carbs || null,
        fat: macroTargets.fat || null
      };

      currentMember.dietaryPreferences = {
        restrictions: updateData.dietaryPreferences.restrictions !== undefined ? updateData.dietaryPreferences.restrictions : (currentMember.dietaryPreferences?.restrictions || []),
        allergies: updateData.dietaryPreferences.allergies !== undefined ? updateData.dietaryPreferences.allergies : (currentMember.dietaryPreferences?.allergies || []),
        dislikedIngredients: updateData.dietaryPreferences.dislikedIngredients !== undefined ? updateData.dietaryPreferences.dislikedIngredients : (currentMember.dietaryPreferences?.dislikedIngredients || []),
        calorieTarget: updateData.dietaryPreferences.calorieTarget !== undefined ? updateData.dietaryPreferences.calorieTarget : (currentMember.dietaryPreferences?.calorieTarget || null),
        macroTargets: validMacroTargets,
        mealFrequency: updateData.dietaryPreferences.mealFrequency !== undefined ? updateData.dietaryPreferences.mealFrequency : (currentMember.dietaryPreferences?.mealFrequency || 3)
      };
    }

    // Save the updated user
    await user.save();

    console.log('✅ Update applied successfully');

    // Verify the update
    const updatedUser = await User.findById(testUserId);
    const updatedAkMember = updatedUser.familyMembers.find(member => member.name === 'Ak');

    console.log('\n🔍 Verifying updated data...');
    console.log('Updated disliked ingredients:', updatedAkMember.dietaryPreferences?.dislikedIngredients);
    console.log('Updated restrictions:', updatedAkMember.dietaryPreferences?.restrictions);
    console.log('Updated allergies:', updatedAkMember.dietaryPreferences?.allergies);

    if (updatedAkMember.dietaryPreferences?.dislikedIngredients?.includes('Elaichi')) {
      console.log('✅ SUCCESS: "Elaichi" was saved correctly in disliked ingredients!');
    } else {
      console.log('❌ FAILED: "Elaichi" was not saved in disliked ingredients');
    }

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n🔌 Disconnected from MongoDB');
  }
}

testUpdateDislikedIngredients();
