const User = require('../models/User');
const Admin = require('../models/Admin');

// Middleware to check if user is admin
const adminAuth = async (req, res, next) => {
  try {
    // First check if user is authenticated (should be done by auth middleware first)
    if (!req.user || !req.user.id) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    // Get user from database to check admin status
    const user = await User.findById(req.user.id);
    if (!user) {
      return res.status(401).json({ message: 'User not found' });
    }

    // Check if user has admin flag
    if (!user.isAdmin) {
      return res.status(403).json({ message: 'Access denied. Admin privileges required.' });
    }

    // Optionally check Admin collection for additional permissions
    const adminRecord = await Admin.findOne({ user: user._id, isActive: true });
    if (adminRecord) {
      req.adminRole = adminRecord.role;
      req.adminPermissions = adminRecord.permissions;
      
      // Update last activity
      adminRecord.lastActivity = new Date();
      await adminRecord.save();
    }

    // Add user info to request
    req.adminUser = user;
    next();
  } catch (error) {
    console.error('Admin auth error:', error);
    res.status(500).json({ message: 'Server error during admin authentication' });
  }
};

// Middleware to check specific admin permissions
const requirePermission = (permission) => {
  return (req, res, next) => {
    if (!req.adminPermissions || !req.adminPermissions.includes(permission)) {
      return res.status(403).json({ 
        message: `Access denied. Required permission: ${permission}` 
      });
    }
    next();
  };
};

// Middleware to check admin role level
const requireRole = (minRole) => {
  const roleHierarchy = {
    'moderator': 1,
    'sub_admin': 2,
    'admin': 3,
    'super_admin': 4
  };

  return (req, res, next) => {
    const userRoleLevel = roleHierarchy[req.adminRole] || 0;
    const requiredRoleLevel = roleHierarchy[minRole] || 0;

    if (userRoleLevel < requiredRoleLevel) {
      return res.status(403).json({
        message: `Access denied. Required role: ${minRole}`
      });
    }
    next();
  };
};

// Middleware to check if user has admin or sub admin privileges
const adminOrSubAdminAuth = async (req, res, next) => {
  try {
    // First check if user is authenticated (should be done by auth middleware first)
    if (!req.user || !req.user.id) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    // Get user from database to check admin status
    const user = await User.findById(req.user.id);
    if (!user) {
      return res.status(401).json({ message: 'User not found' });
    }

    // Check if user has admin flag
    if (!user.isAdmin) {
      return res.status(403).json({ message: 'Access denied. Admin privileges required.' });
    }

    // Check Admin collection for role and permissions
    const adminRecord = await Admin.findOne({ user: user._id, isActive: true });
    if (adminRecord) {
      req.adminRole = adminRecord.role;
      req.adminPermissions = adminRecord.permissions;

      // Update last activity
      adminRecord.lastActivity = new Date();
      await adminRecord.save();
    }

    // Add user info to request
    req.adminUser = user;
    next();
  } catch (error) {
    console.error('Admin auth error:', error);
    res.status(500).json({ message: 'Server error during admin authentication' });
  }
};

// Middleware to check if user can manage meals (admin, sub_admin with meal_management permission)
const canManageMeals = (req, res, next) => {
  if (!req.adminRole) {
    return res.status(403).json({ message: 'Access denied. Admin role required.' });
  }

  // Super admin and admin have full access
  if (req.adminRole === 'super_admin' || req.adminRole === 'admin') {
    return next();
  }

  // Sub admin needs meal_management permission
  if (req.adminRole === 'sub_admin' && req.adminPermissions && req.adminPermissions.includes('meal_management')) {
    return next();
  }

  return res.status(403).json({ message: 'Access denied. Meal management permission required.' });
};

// Middleware to check if user can manage feedback (admin, sub_admin with feedback_management permission)
const canManageFeedback = (req, res, next) => {
  if (!req.adminRole) {
    return res.status(403).json({ message: 'Access denied. Admin role required.' });
  }

  // Super admin and admin have full access
  if (req.adminRole === 'super_admin' || req.adminRole === 'admin') {
    return next();
  }

  // Sub admin needs feedback_management permission
  if (req.adminRole === 'sub_admin' && req.adminPermissions && req.adminPermissions.includes('feedback_management')) {
    return next();
  }

  return res.status(403).json({ message: 'Access denied. Feedback management permission required.' });
};

module.exports = {
  adminAuth,
  requirePermission,
  requireRole,
  adminOrSubAdminAuth,
  canManageMeals,
  canManageFeedback
};
