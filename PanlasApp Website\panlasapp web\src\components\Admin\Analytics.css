.analytics-container {
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
  background: #f8f9fa;
  min-height: 100vh;
  text-align: center; /* Center align all content */
}

.analytics-header {
  text-align: center;
  margin-bottom: 2rem;
}

.analytics-header h1 {
  color: #2c3e50;
  font-size: 2.5rem;
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
}

.analytics-header p {
  color: #7f8c8d;
  font-size: 1.1rem;
}

/* Filters */
.analytics-filters {
  display: flex;
  gap: 2rem;
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  flex-wrap: wrap;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.filter-group label {
  font-weight: 600;
  color: #2c3e50;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.filter-group select {
  padding: 0.75rem;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 1rem;
  background: white;
  cursor: pointer;
  transition: border-color 0.3s ease;
}

.filter-group select:focus {
  outline: none;
  border-color: #20C5AF;
}

/* Overview Stats */
.analytics-overview {
  margin-bottom: 2rem;
}

.analytics-overview h2 {
  color: #2c3e50;
  margin-bottom: 1rem;
  font-size: 1.8rem;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
  justify-content: center;
  justify-items: center;
}

.stat-card {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 1.5rem;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
}

.total-users .stat-icon { background: #20C5AF }
.active-users .stat-icon { background: #20C5AF }
.admin-users .stat-icon { background: #20C5AF }
.regular-users .stat-icon { background: #20C5AF}

.stat-content h3 {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0;
  color: #2c3e50;
}

.stat-content p {
  margin: 0;
  color: #7f8c8d;
  font-size: 1.1rem;
  font-weight: 500;
}

/* Session Stats */
.session-stats {
  margin-bottom: 2rem;
}

.session-stats h2 {
  color: #2c3e50;
  margin-bottom: 1rem;
  font-size: 1.8rem;
}

.session-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  justify-content: center;
  justify-items: center;
}

.session-card {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.session-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: #20C5AF;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
}

.session-content h3 {
  font-size: 2rem;
  font-weight: 700;
  margin: 0;
  color: #2c3e50;
}

.session-content p {
  margin: 0.5rem 0;
  color: #7f8c8d;
  font-size: 1.1rem;
}

.session-breakdown {
  display: flex;
  gap: 1rem;
  margin-top: 0.5rem;
}

.admin-count, .user-count {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 600;
}

.admin-count {
  background: #fee2e2;
  color: light red;
}

.user-count {
  background: #dbeafe;
  color: light blue;
}

/* Platform Analytics */
.platform-analytics {
  margin-bottom: 2rem;
}

.platform-analytics h2 {
  color: #2c3e50;
  margin-bottom: 1rem;
  font-size: 1.8rem;
}

.platform-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  justify-content: center;
  justify-items: center;
}

.platform-card {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.platform-icon {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: #20C5AF;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  color: white;
  margin: 0 auto 1rem;
}

.platform-content h3 {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: #2c3e50;
  text-transform: capitalize;
}

.platform-stats p {
  margin: 0.5rem 0;
  color: #7f8c8d;
  font-size: 1rem;
}

.platform-breakdown {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-top: 1rem;
}

.admin-events, .user-events {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 600;
}

.admin-events {
  background: #fee2e2;
  color: light red;
}

.user-events {
  background: #dbeafe;
  color: light blue;
}

/* Event Analytics */
.event-analytics {
  margin-bottom: 2rem;
  text-align: center;
}

.event-analytics h2 {
  color: #2c3e50;
  margin-bottom: 1rem;
  font-size: 1.8rem;
}

.events-table {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  margin: 0 auto;
  max-width: 800px;
}

.events-table table {
  width: 100%;
  border-collapse: collapse;
}

.events-table th {
  background: #20C5AF;
  color: white;
  padding: 1rem;
  text-align: left;
  font-weight: 600;
}

.events-table td {
  padding: 1rem;
  border-bottom: 1px solid #ecf0f1;
}

.events-table tr:hover {
  background: #f8f9fa;
}

.event-name {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  color: #2c3e50;
}

/* Daily Trends */
.trends-analytics {
  margin-bottom: 2rem;
  text-align: center;
}

.trends-analytics h2 {
  color: #2c3e50;
  margin-bottom: 1rem;
  font-size: 1.8rem;
}

.trends-chart {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  display: flex;
  gap: 1rem;
  overflow-x: auto;
  min-height: 200px;
  align-items: flex-end;
  justify-content: center;
  margin: 0 auto;
  max-width: 1000px;
}

.trend-day {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 80px;
}

.trend-date {
  font-size: 0.8rem;
  color: #7f8c8d;
  margin-bottom: 0.5rem;
  transform: rotate(-45deg);
  white-space: nowrap;
}

.trend-bars {
  display: flex;
  gap: 2px;
  height: 120px;
  align-items: flex-end;
  margin-bottom: 0.5rem;
}

.trend-bar {
  width: 15px;
  min-height: 5px;
  border-radius: 2px;
  transition: all 0.3s ease;
}

.trend-bar.admin {
  background: linear-gradient(to top, #e74c3c, #c0392b);
}

.trend-bar.user {
  background: linear-gradient(to top, #3498db, #2980b9);
}

.trend-total {
  font-size: 0.9rem;
  font-weight: 600;
  color: #2c3e50;
}

/* Loading and Error States */
.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  gap: 1rem;
}

.spinner {
  width: 50px;
  height: 50px;
  border: 4px solid #ecf0f1;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: circleSpinner 1s linear infinite;
}

/* Use consistent circle loader */
.analytics-circle-loader {
  width: 50px;
  height: 50px;
  border: 4px solid #ecf0f1;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: circleSpinner 1s linear infinite;
}

@keyframes circleSpinner {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-message {
  text-align: center;
  padding: 2rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.retry-btn {
  background: #3498db;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1rem;
  margin-top: 1rem;
  transition: background 0.3s ease;
}

.retry-btn:hover {
  background: #2980b9;
}

/* Responsive Design */
@media (max-width: 768px) {
  .analytics-container {
    padding: 1rem;
  }

  .analytics-filters {
    flex-direction: column;
    gap: 1rem;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .session-grid,
  .platform-grid {
    grid-template-columns: 1fr;
  }

  .trends-chart {
    padding: 1rem;
  }

  .analytics-header h1 {
    font-size: 2rem;
  }
}
