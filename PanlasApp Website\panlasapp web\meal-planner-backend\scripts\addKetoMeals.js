const mongoose = require('mongoose');
const Meal = require('../models/Meal');

// Connect to MongoDB
mongoose.connect(process.env.MONGODB_URI || 'mongodb+srv://johnmatthewmartin09:<EMAIL>/mealplanner?retryWrites=true&w=majority&appName=MealPlannersCluster', {
  useNewUrlParser: true,
  useUnifiedTopology: true,
});

const ketoFilipineMeals = [
  // Breakfast Keto Meals
  {
    name: "Keto Longsilog",
    mealType: ["breakfast"],
    category: ["breakfast"],
    dietaryTags: ["keto", "low-carb", "high-fat"],
    calories: 450,
    protein: 25,
    carbs: 5,
    fat: 35,
    image: "longsilog_keto.jpg",
    description: "Traditional Filipino longganisa with fried egg, served without rice. Perfect keto breakfast with high fat and low carbs.",
    ingredients: ["longganisa", "eggs", "garlic", "onions", "coconut oil"],
    instructions: ["Fry longganisa until crispy", "Fry eggs in coconut oil", "Serve hot without rice"],
    allergens: [],
    dietType: {
      isVegetarian: false,
      isVegan: false,
      isGlutenFree: true,
      isDairyFree: true,
      isNutFree: true,
      isKeto: true,
      isLowCarb: true,
      isPaleo: true
    }
  },
  {
    name: "Keto Tapsilog",
    mealType: ["breakfast"],
    category: ["breakfast"],
    dietaryTags: ["keto", "low-carb", "high-fat"],
    calories: 420,
    protein: 30,
    carbs: 4,
    fat: 32,
    image: "tapsilog_keto.jpg",
    description: "Filipino beef tapa with fried egg, no rice. High protein keto breakfast.",
    ingredients: ["beef tapa", "eggs", "garlic", "soy sauce", "coconut oil"],
    instructions: ["Pan-fry beef tapa", "Fry eggs sunny side up", "Serve immediately"],
    allergens: ["Soy"],
    dietType: {
      isVegetarian: false,
      isVegan: false,
      isGlutenFree: false,
      isDairyFree: true,
      isNutFree: true,
      isKeto: true,
      isLowCarb: true,
      isPaleo: false
    }
  },
  {
    name: "Keto Tortang Talong",
    mealType: ["breakfast"],
    category: ["breakfast"],
    dietaryTags: ["keto", "low-carb", "vegetarian"],
    calories: 180,
    protein: 12,
    carbs: 8,
    fat: 12,
    image: "tortang_talong_keto.jpg",
    description: "Grilled eggplant omelet, a keto-friendly Filipino breakfast classic.",
    ingredients: ["eggplant", "eggs", "onions", "garlic", "coconut oil"],
    instructions: ["Grill eggplant until soft", "Beat eggs with seasonings", "Dip eggplant in egg mixture and fry"],
    allergens: [],
    dietType: {
      isVegetarian: true,
      isVegan: false,
      isGlutenFree: true,
      isDairyFree: true,
      isNutFree: true,
      isKeto: true,
      isLowCarb: true,
      isPaleo: true
    }
  },

  // Lunch Keto Meals
  {
    name: "Keto Adobong Manok",
    mealType: ["lunch", "dinner"],
    category: ["lunch", "dinner"],
    dietaryTags: ["keto", "low-carb", "high-fat"],
    calories: 380,
    protein: 35,
    carbs: 6,
    fat: 24,
    image: "adobong_manok_keto.jpg",
    description: "Classic Filipino chicken adobo without sugar, perfect for keto diet.",
    ingredients: ["chicken thighs", "soy sauce", "vinegar", "garlic", "bay leaves", "coconut oil"],
    instructions: ["Marinate chicken in soy sauce and vinegar", "Brown chicken in coconut oil", "Simmer until tender"],
    allergens: ["Soy"],
    dietType: {
      isVegetarian: false,
      isVegan: false,
      isGlutenFree: false,
      isDairyFree: true,
      isNutFree: true,
      isKeto: true,
      isLowCarb: true,
      isPaleo: false
    }
  },
  {
    name: "Keto Sisig",
    mealType: ["lunch", "dinner"],
    category: ["lunch", "dinner"],
    dietaryTags: ["keto", "low-carb", "high-fat"],
    calories: 450,
    protein: 28,
    carbs: 7,
    fat: 35,
    image: "sisig_keto.jpg",
    description: "Sizzling pork sisig without rice, loaded with healthy fats.",
    ingredients: ["pork belly", "pork liver", "onions", "chili", "calamansi", "coconut oil"],
    instructions: ["Boil and grill pork", "Chop finely", "Sauté with onions and seasonings"],
    allergens: [],
    dietType: {
      isVegetarian: false,
      isVegan: false,
      isGlutenFree: true,
      isDairyFree: true,
      isNutFree: true,
      isKeto: true,
      isLowCarb: true,
      isPaleo: true
    }
  },
  {
    name: "Keto Tinolang Manok",
    mealType: ["lunch", "dinner"],
    category: ["lunch", "dinner"],
    dietaryTags: ["keto", "low-carb", "soup"],
    calories: 280,
    protein: 25,
    carbs: 9,
    fat: 16,
    image: "tinola_keto.jpg",
    description: "Filipino chicken soup with malunggay and sayote, no rice.",
    ingredients: ["chicken", "malunggay leaves", "sayote", "ginger", "onions", "fish sauce"],
    instructions: ["Sauté ginger and onions", "Add chicken and water", "Simmer with vegetables"],
    allergens: [],
    dietType: {
      isVegetarian: false,
      isVegan: false,
      isGlutenFree: true,
      isDairyFree: true,
      isNutFree: true,
      isKeto: true,
      isLowCarb: true,
      isPaleo: true
    }
  },

  // Dinner Keto Meals
  {
    name: "Keto Lechon Kawali",
    mealType: ["dinner"],
    category: ["dinner"],
    dietaryTags: ["keto", "low-carb", "high-fat"],
    calories: 520,
    protein: 32,
    carbs: 3,
    fat: 42,
    image: "lechon_kawali_keto.jpg",
    description: "Crispy pork belly without rice, pure keto indulgence.",
    ingredients: ["pork belly", "salt", "pepper", "bay leaves", "coconut oil"],
    instructions: ["Boil pork belly with seasonings", "Deep fry until crispy", "Serve with low-carb dipping sauce"],
    allergens: [],
    dietType: {
      isVegetarian: false,
      isVegan: false,
      isGlutenFree: true,
      isDairyFree: true,
      isNutFree: true,
      isKeto: true,
      isLowCarb: true,
      isPaleo: true
    }
  },
  {
    name: "Keto Laing",
    mealType: ["lunch", "dinner"],
    category: ["lunch", "dinner"],
    dietaryTags: ["keto", "low-carb", "coconut"],
    calories: 220,
    protein: 8,
    carbs: 10,
    fat: 18,
    image: "laing_keto.jpg",
    description: "Taro leaves in coconut milk, naturally keto-friendly Filipino dish.",
    ingredients: ["taro leaves", "coconut milk", "shrimp paste", "chili", "ginger"],
    instructions: ["Sauté aromatics", "Add taro leaves", "Simmer in coconut milk"],
    allergens: ["Shellfish"],
    dietType: {
      isVegetarian: false,
      isVegan: false,
      isGlutenFree: true,
      isDairyFree: true,
      isNutFree: false,
      isKeto: true,
      isLowCarb: true,
      isPaleo: true
    }
  },
  {
    name: "Keto Bicol Express",
    mealType: ["lunch", "dinner"],
    category: ["lunch", "dinner"],
    dietaryTags: ["keto", "low-carb", "spicy"],
    calories: 350,
    protein: 20,
    carbs: 8,
    fat: 28,
    image: "bicol_express_keto.jpg",
    description: "Spicy pork in coconut milk, perfect keto Filipino dish.",
    ingredients: ["pork belly", "coconut milk", "chili", "shrimp paste", "onions"],
    instructions: ["Sauté pork until crispy", "Add coconut milk and spices", "Simmer until thick"],
    allergens: ["Shellfish"],
    dietType: {
      isVegetarian: false,
      isVegan: false,
      isGlutenFree: true,
      isDairyFree: true,
      isNutFree: false,
      isKeto: true,
      isLowCarb: true,
      isPaleo: true
    }
  },

  // Snack/Side Keto Meals
  {
    name: "Keto Ensaladang Pipino",
    mealType: ["snack"],
    category: ["snack"],
    dietaryTags: ["keto", "low-carb", "fresh", "vegetarian"],
    calories: 120,
    protein: 3,
    carbs: 6,
    fat: 10,
    image: "ensaladang_pipino_keto.jpg",
    description: "Fresh cucumber salad with coconut vinegar dressing.",
    ingredients: ["cucumber", "tomatoes", "onions", "coconut vinegar", "coconut oil"],
    instructions: ["Slice vegetables thinly", "Mix with vinegar and oil", "Chill before serving"],
    allergens: [],
    dietType: {
      isVegetarian: true,
      isVegan: true,
      isGlutenFree: true,
      isDairyFree: true,
      isNutFree: false,
      isKeto: true,
      isLowCarb: true,
      isPaleo: true
    }
  }
];

async function addKetoMeals() {
  try {
    console.log('🍽️ Adding Filipino keto meals to database...\n');
    
    // Check if meals already exist
    for (const meal of ketoFilipineMeals) {
      const existingMeal = await Meal.findOne({ name: meal.name });
      
      if (existingMeal) {
        console.log(`⚠️ Meal "${meal.name}" already exists, skipping...`);
        continue;
      }
      
      const newMeal = new Meal(meal);
      await newMeal.save();
      console.log(`✅ Added: ${meal.name} (${meal.carbs}g carbs, ${meal.calories} cal)`);
    }
    
    console.log('\n🎉 Successfully added Filipino keto meals!');
    console.log(`📊 Total meals added: ${ketoFilipineMeals.length}`);
    
    // Verify the meals were added
    const ketoMealsInDb = await Meal.find({ 'dietType.isKeto': true });
    console.log(`🔍 Total keto meals in database: ${ketoMealsInDb.length}`);
    
    const lowCarbMeals = await Meal.find({ carbs: { $lte: 10 } });
    console.log(`🥬 Total low-carb meals (≤10g): ${lowCarbMeals.length}`);
    
  } catch (error) {
    console.error('❌ Error adding keto meals:', error);
  } finally {
    mongoose.connection.close();
  }
}

// Run the script
addKetoMeals();
