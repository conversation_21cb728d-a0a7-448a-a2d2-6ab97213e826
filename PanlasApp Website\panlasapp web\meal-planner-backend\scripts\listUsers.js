require('dotenv').config({ path: __dirname + '/../.env' });
const mongoose = require('mongoose');
const User = require('../models/User');

const listUsers = async () => {
  try {
    console.log('🔗 Connecting to MongoDB...');
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ Connected to MongoDB successfully');

    console.log('\n📋 Listing all users in the database:');
    
    // Find all users and display their basic info
    const users = await User.find({}, {
      username: 1,
      email: 1,
      firstName: 1,
      lastName: 1,
      isEmailVerified: 1,
      createdAt: 1
    }).sort({ createdAt: -1 });
    
    if (users.length === 0) {
      console.log('❌ No users found in database');
      return;
    }
    
    console.log(`✅ Found ${users.length} users:`);
    console.log('─'.repeat(100));
    
    users.forEach((user, index) => {
      console.log(`${index + 1}. ID: ${user._id}`);
      console.log(`   Username: ${user.username}`);
      console.log(`   Email: ${user.email}`);
      console.log(`   Name: ${user.firstName} ${user.lastName}`);
      console.log(`   Email Verified: ${user.isEmailVerified}`);
      console.log(`   Created: ${user.createdAt}`);
      console.log('─'.repeat(50));
    });
    
    // Check for specific conflicts
    console.log('\n🔍 Checking for potential conflicts:');
    
    const usernameToCheck = 'umerfarooq';
    const emailToCheck = '<EMAIL>';
    
    const userByUsername = await User.findOne({ username: usernameToCheck });
    const userByEmail = await User.findOne({ email: emailToCheck.toLowerCase() });
    
    if (userByUsername) {
      console.log(`⚠️  Username "${usernameToCheck}" is taken by:`);
      console.log(`   Email: ${userByUsername.email}`);
      console.log(`   Name: ${userByUsername.firstName} ${userByUsername.lastName}`);
    } else {
      console.log(`✅ Username "${usernameToCheck}" is available`);
    }
    
    if (userByEmail) {
      console.log(`⚠️  Email "${emailToCheck}" is taken by:`);
      console.log(`   Username: ${userByEmail.username}`);
      console.log(`   Name: ${userByEmail.firstName} ${userByEmail.lastName}`);
    } else {
      console.log(`✅ Email "${emailToCheck}" is available`);
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    console.error('Stack trace:', error.stack);
  } finally {
    console.log('\n🔌 Closing database connection...');
    await mongoose.connection.close();
    console.log('✅ Database connection closed');
    process.exit(0);
  }
};

// Run the script
console.log('🚀 Starting user listing script...');
listUsers();
