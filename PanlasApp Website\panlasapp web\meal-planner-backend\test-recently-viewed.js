// Test script to verify recently viewed meals functionality
const axios = require('axios');

const API_BASE_URL = 'http://localhost:5000/api';

// Test user credentials (you'll need to use real credentials)
const testUser = {
  email: '<EMAIL>',
  password: 'testpassword'
};

// Test meal data
const testMeal = {
  id: 'test-meal-1',
  name: 'Test Adobo',
  category: 'Main Course',
  calories: 350,
  protein: 25,
  carbs: 15,
  fat: 20,
  image: 'https://example.com/adobo.jpg',
  description: 'Delicious Filipino adobo',
  ingredients: ['chicken', 'soy sauce', 'vinegar'],
  instructions: ['Cook chicken', 'Add sauce', 'Simmer']
};

async function testRecentlyViewedMeals() {
  try {
    console.log('=== TESTING RECENTLY VIEWED MEALS ===');
    
    // Step 1: Login to get token
    console.log('1. Logging in...');
    const loginResponse = await axios.post(`${API_BASE_URL}/users/login`, testUser);
    const token = loginResponse.data.token;
    
    if (!token) {
      console.error('Failed to get token from login');
      return;
    }
    
    console.log('✓ Login successful, token received');
    
    const headers = {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    };
    
    // Step 2: Get initial recently viewed meals (should be empty)
    console.log('2. Getting initial recently viewed meals...');
    const initialResponse = await axios.get(`${API_BASE_URL}/users/recently-viewed-meals`, { headers });
    console.log('Initial recently viewed meals:', initialResponse.data);
    
    // Step 3: Add a meal to recently viewed
    console.log('3. Adding meal to recently viewed...');
    const addResponse = await axios.post(`${API_BASE_URL}/users/recently-viewed-meals`, 
      { meal: testMeal }, 
      { headers }
    );
    console.log('Add meal response:', addResponse.data);
    
    // Step 4: Get recently viewed meals again (should contain the meal)
    console.log('4. Getting recently viewed meals after adding...');
    const finalResponse = await axios.get(`${API_BASE_URL}/users/recently-viewed-meals`, { headers });
    console.log('Final recently viewed meals:', finalResponse.data);
    
    // Verify the meal was added
    const meals = finalResponse.data.recentlyViewedMeals || [];
    if (meals.length > 0 && meals[0].name === testMeal.name) {
      console.log('✓ SUCCESS: Meal was successfully added to recently viewed!');
    } else {
      console.log('✗ FAILED: Meal was not found in recently viewed');
    }
    
  } catch (error) {
    console.error('Test failed:', error.response?.data || error.message);
  }
}

// Run the test
testRecentlyViewedMeals();
