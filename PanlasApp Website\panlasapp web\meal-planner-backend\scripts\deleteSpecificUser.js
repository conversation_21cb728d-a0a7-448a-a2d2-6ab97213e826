require('dotenv').config({ path: __dirname + '/../.env' });
const mongoose = require('mongoose');
const User = require('../models/User');

const deleteSpecificUser = async () => {
  try {
    console.log('🔗 Connecting to MongoDB...');
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ Connected to MongoDB successfully');

    const emailToDelete = '<EMAIL>';
    
    console.log(`\n🔍 Searching for user with email: ${emailToDelete}`);
    
    // Find the user first to see if they exist
    const user = await User.findOne({ email: emailToDelete.toLowerCase() });
    
    if (!user) {
      console.log(`❌ No user found with email: ${emailToDelete}`);
      return;
    }
    
    console.log(`✅ Found user:`);
    console.log(`   - ID: ${user._id}`);
    console.log(`   - Username: ${user.username}`);
    console.log(`   - Email: ${user.email}`);
    console.log(`   - First Name: ${user.firstName}`);
    console.log(`   - Last Name: ${user.lastName}`);
    console.log(`   - Created: ${user.createdAt}`);
    console.log(`   - Email Verified: ${user.isEmailVerified}`);
    
    // Delete the user
    console.log(`\n🗑️  Deleting user...`);
    const deleteResult = await User.deleteOne({ email: emailToDelete.toLowerCase() });
    
    if (deleteResult.deletedCount === 1) {
      console.log(`✅ Successfully deleted user: ${emailToDelete}`);
    } else {
      console.log(`❌ Failed to delete user: ${emailToDelete}`);
    }
    
    // Verify deletion
    const verifyUser = await User.findOne({ email: emailToDelete.toLowerCase() });
    if (!verifyUser) {
      console.log(`✅ Verified: User ${emailToDelete} no longer exists in database`);
    } else {
      console.log(`❌ Error: User ${emailToDelete} still exists in database`);
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    console.error('Stack trace:', error.stack);
  } finally {
    console.log('\n🔌 Closing database connection...');
    await mongoose.connection.close();
    console.log('✅ Database connection closed');
    process.exit(0);
  }
};

// Run the script
console.log('🚀 Starting user deletion script...');
console.log('📧 Target email: <EMAIL>');
console.log('⚠️  This will permanently delete the user from the database!');

deleteSpecificUser();
