const mongoose = require('mongoose');
const User = require('../models/User');
const MealPlan = require('../models/MealPlan');
const Meal = require('../models/Meal');
const { updateFutureMealPlansForDietaryChanges, checkMealConflict } = require('../services/mealPlanUpdateService');

// Connect to MongoDB
mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/panlas-app', {
  useNewUrlParser: true,
  useUnifiedTopology: true,
});

async function testAdobongKangkongScenario() {
  try {
    console.log('🧪 Testing Adobong Kangkong scenario...\n');
    
    // First, let's check what Adobong Kangkong looks like in the database
    const adobongKangkong = await Meal.findOne({ name: /Adobong Kangkong/i });
    
    if (!adobongKangkong) {
      console.log('❌ Adobong Kangkong not found in database');
      return;
    }
    
    console.log('📋 Adobong Kangkong details:');
    console.log(`Name: ${adobongKangkong.name}`);
    console.log(`Dietary Tags: ${adobongKangkong.dietaryTags?.join(', ') || 'None'}`);
    console.log(`Diet Type:`, adobongKangkong.dietType);
    console.log(`Allergens: ${adobongKangkong.allergens?.join(', ') || 'None'}`);
    console.log(`Ingredients: ${adobongKangkong.ingredients?.join(', ') || 'None'}`);
    
    // Test different dietary preference scenarios
    console.log('\n🔍 Testing conflict detection:\n');
    
    // Scenario 1: Vegan to Gluten-Free (should NOT conflict)
    console.log('1. Changing from Vegan to Gluten-Free:');
    const glutenFreePrefs = {
      restrictions: ['Gluten-Free'],
      allergies: ['Milk'],
      dislikedIngredients: []
    };
    
    const glutenFreeConflict = checkMealConflict(adobongKangkong, glutenFreePrefs);
    console.log(`   Conflict detected: ${glutenFreeConflict.hasConflict}`);
    if (glutenFreeConflict.hasConflict) {
      console.log(`   Reasons: ${glutenFreeConflict.reasons.join(', ')}`);
    } else {
      console.log('   ✅ No conflict - Adobong Kangkong IS gluten-free');
    }
    
    // Scenario 2: Vegan to restrictions that would conflict
    console.log('\n2. Changing to restrictions that SHOULD conflict:');
    const conflictingPrefs = {
      restrictions: ['Keto'], // Adobong Kangkong is not keto (12g carbs)
      allergies: [],
      dislikedIngredients: []
    };
    
    const ketoConflict = checkMealConflict(adobongKangkong, conflictingPrefs);
    console.log(`   Conflict detected: ${ketoConflict.hasConflict}`);
    if (ketoConflict.hasConflict) {
      console.log(`   Reasons: ${ketoConflict.reasons.join(', ')}`);
      console.log('   ✅ This SHOULD trigger a replacement');
    }
    
    // Scenario 3: Adding allergies that conflict
    console.log('\n3. Adding soy sauce allergy (should conflict):');
    const soyAllergyPrefs = {
      restrictions: ['Vegan'],
      allergies: ['Soy'], // Adobong Kangkong contains soy sauce
      dislikedIngredients: []
    };
    
    const soyConflict = checkMealConflict(adobongKangkong, soyAllergyPrefs);
    console.log(`   Conflict detected: ${soyConflict.hasConflict}`);
    if (soyConflict.hasConflict) {
      console.log(`   Reasons: ${soyConflict.reasons.join(', ')}`);
      console.log('   ✅ This SHOULD trigger a replacement');
    }
    
    // Scenario 4: Adding disliked ingredients
    console.log('\n4. Adding kangkong as disliked ingredient:');
    const kangkongDislikedPrefs = {
      restrictions: ['Vegan'],
      allergies: [],
      dislikedIngredients: ['kangkong']
    };
    
    const kangkongConflict = checkMealConflict(adobongKangkong, kangkongDislikedPrefs);
    console.log(`   Conflict detected: ${kangkongConflict.hasConflict}`);
    if (kangkongConflict.hasConflict) {
      console.log(`   Reasons: ${kangkongConflict.reasons.join(', ')}`);
      console.log('   ✅ This SHOULD trigger a replacement');
    }
    
    console.log('\n📊 Summary:');
    console.log('Your scenario (Vegan → Gluten-Free) did NOT trigger replacement because:');
    console.log('• Adobong Kangkong IS marked as gluten-free in the database');
    console.log('• The system correctly determined no conflict exists');
    console.log('• This is the expected behavior!');
    
    console.log('\n💡 To test the replacement feature, try:');
    console.log('• Change to "Keto" restriction (Adobong Kangkong has 12g carbs, not keto)');
    console.log('• Add "Soy" allergy (Adobong Kangkong contains soy sauce)');
    console.log('• Add "kangkong" as disliked ingredient');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    mongoose.connection.close();
  }
}

// Run the test
testAdobongKangkongScenario();
