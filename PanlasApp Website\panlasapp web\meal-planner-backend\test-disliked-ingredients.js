require('dotenv').config();
const mongoose = require('mongoose');
const User = require('./models/User');

async function testDislikedIngredients() {
  try {
    console.log('🔍 Connecting to MongoDB...');
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ Connected to MongoDB');

    // Find a test user (you can replace this with your actual user ID)
    const testUserId = '685883ea3cc2df1d683b8714'; // Replace with your user ID
    
    console.log('\n🔍 Looking for user:', testUserId);
    const user = await User.findById(testUserId);
    
    if (!user) {
      console.log('❌ User not found');
      return;
    }

    console.log('✅ User found:', user.name);
    console.log('📋 Current family members:', user.familyMembers.length);

    // Test adding a family member with disliked ingredients
    const testMember = {
      name: 'Test Member with Disliked Ingredients',
      dateOfBirth: new Date('1990-01-01'),
      dietaryPreferences: {
        restrictions: ['Vegetarian'],
        allergies: ['Peanuts'],
        dislikedIngredients: ['Mushrooms', 'Onions', 'Garlic'],
        calorieTarget: null,
        macroTargets: {
          protein: null,
          carbs: null,
          fat: null
        },
        mealFrequency: 3
      }
    };

    console.log('\n🧪 Adding test family member with disliked ingredients...');
    console.log('Test member data:', JSON.stringify(testMember, null, 2));

    user.familyMembers.push(testMember);
    await user.save();

    console.log('✅ Test member added successfully');

    // Fetch the user again to verify the data was saved correctly
    const updatedUser = await User.findById(testUserId);
    const addedMember = updatedUser.familyMembers[updatedUser.familyMembers.length - 1];

    console.log('\n🔍 Verifying saved data...');
    console.log('Added member name:', addedMember.name);
    console.log('Disliked ingredients:', addedMember.dietaryPreferences.dislikedIngredients);
    console.log('Restrictions:', addedMember.dietaryPreferences.restrictions);
    console.log('Allergies:', addedMember.dietaryPreferences.allergies);

    if (addedMember.dietaryPreferences.dislikedIngredients.length > 0) {
      console.log('✅ SUCCESS: Disliked ingredients were saved correctly!');
    } else {
      console.log('❌ FAILED: Disliked ingredients were not saved');
    }

    // Clean up - remove the test member
    console.log('\n🧹 Cleaning up test data...');
    updatedUser.familyMembers = updatedUser.familyMembers.filter(
      member => member.name !== 'Test Member with Disliked Ingredients'
    );
    await updatedUser.save();
    console.log('✅ Test data cleaned up');

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n🔌 Disconnected from MongoDB');
  }
}

testDislikedIngredients();
