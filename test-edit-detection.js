// Test script to verify edit detection logic

const directEditPatterns = [
  /replace\s+(.+?)\s+with\s+(.+)/i,
  /change\s+(.+?)\s+to\s+(.+)/i,
  /swap\s+(.+?)\s+with\s+(.+)/i,
  /substitute\s+(.+?)\s+with\s+(.+)/i,
  /i want to replace\s+(.+?)\s+with\s+(.+)/i,
  /i want to change\s+(.+?)\s+to\s+(.+)/i,
  /i want to swap\s+(.+?)\s+with\s+(.+)/i
];

const testCases = [
  "Replace Chicken Curry with Sinanbag",
  "I want to replace Pinak<PERSON> with <PERSON><PERSON><PERSON>",
  "Change breakfast to something vegetarian",
  "edit",
  "update",
  "edit the meal plan",
  "update my meal plan"
];

console.log("Testing edit detection logic:\n");

testCases.forEach(testCase => {
  const isDirectEditRequest = directEditPatterns.some(pattern => pattern.test(testCase.toLowerCase()));
  const editKeywords = ['edit', 'update'];
  const hasGeneralEditKeyword = editKeywords.some(keyword => testCase.toLowerCase().includes(keyword));
  
  console.log(`"${testCase}"`);
  console.log(`  Direct Edit Request: ${isDirectEditRequest}`);
  console.log(`  General Edit Keyword: ${hasGeneralEditKeyword}`);
  console.log(`  Action: ${isDirectEditRequest ? 'Process immediately' : hasGeneralEditKeyword ? 'Enter edit mode' : 'No action'}`);
  console.log('');
});
