import React, { useState, useEffect } from 'react';
import { 
  <PERSON>a<PERSON>sers, 
  FaUser<PERSON>heck, 
  FaUserTimes, 
  FaChartLine, 
  FaMobile, 
  FaDesktop, 
  FaTabletAlt,
  FaSignInAlt,
  FaSignOutAlt,
  FaEye,
  FaCalendarAlt,
  FaFilter
} from 'react-icons/fa';
import analyticsService from '../../services/analyticsService';
import Layout from '../Layout/Layout';
import './Analytics.css';

const Analytics = () => {
  const [analyticsData, setAnalyticsData] = useState(null);
  const [userActivityData, setUserActivityData] = useState(null);
  const [platformData, setPlatformData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  
  // Filters
  const [timeRange, setTimeRange] = useState('7d');
  const [userType, setUserType] = useState('all');
  const [activityStatus, setActivityStatus] = useState('all');

  useEffect(() => {
    fetchAnalyticsData();
  }, [timeRange, userType, activityStatus]);

  const fetchAnalyticsData = async () => {
    try {
      setLoading(true);
      setError(null);

      const [dashboardData, userActivity, platformAnalytics] = await Promise.all([
        analyticsService.getDashboardAnalytics(timeRange, userType),
        analyticsService.getUserActivityAnalytics(timeRange, activityStatus),
        analyticsService.getPlatformAnalytics(timeRange)
      ]);

      setAnalyticsData(dashboardData.data);
      setUserActivityData(userActivity.data);
      setPlatformData(platformAnalytics.data);
    } catch (err) {
      setError('Failed to fetch analytics data');
      console.error('Analytics fetch error:', err);
    } finally {
      setLoading(false);
    }
  };

  const formatNumber = (num) => {
    if (num >= 1000000) return (num / 1000000).toFixed(1) + 'M';
    if (num >= 1000) return (num / 1000).toFixed(1) + 'K';
    return num?.toString() || '0';
  };

  const getTimeRangeLabel = (range) => {
    const labels = {
      '1d': 'Last 24 Hours',
      '7d': 'Last 7 Days',
      '30d': 'Last 30 Days',
      '90d': 'Last 90 Days'
    };
    return labels[range] || 'Last 7 Days';
  };

  if (loading) {
    return (
      <Layout>
        <div className="analytics-container">
          <div className="loading-state">
            <div className="circle-loader large primary"></div>
            <p>Loading analytics data...</p>
          </div>
        </div>
      </Layout>
    );
  }

  if (error) {
    return (
      <Layout>
        <div className="analytics-container">
          <div className="error-message">
            <p>{error}</p>
            <button onClick={fetchAnalyticsData} className="retry-btn">
              Retry
            </button>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="analytics-container">
        {/* Header */}
        <div className="analytics-header">
          <h1><FaChartLine /> Analytics Dashboard</h1>
          <p>Comprehensive insights into user behavior and platform usage</p>
        </div>

        {/* Filters */}
        <div className="analytics-filters">
          <div className="filter-group">
            <label><FaCalendarAlt /> Time Range:</label>
            <select 
              value={timeRange} 
              onChange={(e) => setTimeRange(e.target.value)}
            >
              <option value="1d">Last 24 Hours</option>
              <option value="7d">Last 7 Days</option>
              <option value="30d">Last 30 Days</option>
              <option value="90d">Last 90 Days</option>
            </select>
          </div>

          <div className="filter-group">
            <label><FaUsers /> User Type:</label>
            <select 
              value={userType} 
              onChange={(e) => setUserType(e.target.value)}
            >
              <option value="all">All Users</option>
              <option value="admin">Admin Users</option>
              <option value="user">Regular Users</option>
            </select>
          </div>

          <div className="filter-group">
            <label><FaFilter /> Activity Status:</label>
            <select 
              value={activityStatus} 
              onChange={(e) => setActivityStatus(e.target.value)}
            >
              <option value="all">All Users</option>
              <option value="active">Active Users</option>
              <option value="inactive">Inactive Users</option>
            </select>
          </div>
        </div>

        {/* Overview Stats */}
        {analyticsData && (
          <div className="analytics-overview">
            <h2>Overview - {getTimeRangeLabel(timeRange)}</h2>
            <div className="stats-grid">
              <div className="stat-card total-users">
                <div className="stat-icon">
                  <FaUsers />
                </div>
                <div className="stat-content">
                  <h3>{formatNumber(analyticsData.userStats.totalUsers)}</h3>
                  <p>Total Users</p>
                </div>
              </div>

              <div className="stat-card active-users">
                <div className="stat-icon">
                  <FaUserCheck />
                </div>
                <div className="stat-content">
                  <h3>{formatNumber(analyticsData.userStats.activeUsers)}</h3>
                  <p>Active Users</p>
                </div>
              </div>

              <div className="stat-card admin-users">
                <div className="stat-icon">
                  <FaUserTimes />
                </div>
                <div className="stat-content">
                  <h3>{formatNumber(analyticsData.userStats.adminUsers)}</h3>
                  <p>Admin Users</p>
                </div>
              </div>

              <div className="stat-card regular-users">
                <div className="stat-icon">
                  <FaUsers />
                </div>
                <div className="stat-content">
                  <h3>{formatNumber(analyticsData.userStats.regularUsers)}</h3>
                  <p>Regular Users</p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Session Statistics */}
        {analyticsData?.sessionStats && (
          <div className="session-stats">
            <h2>Session Activity</h2>
            <div className="session-grid">
              {analyticsData.sessionStats.map((stat) => (
                <div key={stat._id} className="session-card">
                  <div className="session-icon">
                    {stat._id === 'login' ? <FaSignInAlt /> : <FaSignOutAlt />}
                  </div>
                  <div className="session-content">
                    <h3>{stat.total}</h3>
                    <p>{stat._id === 'login' ? 'Logins' : 'Logouts'}</p>
                    <div className="session-breakdown">
                      <span className="admin-count">Admin: {stat.admin}</span>
                      <span className="user-count">Users: {stat.user}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Platform Usage */}
        {platformData && (
          <div className="platform-analytics">
            <h2>Platform Usage</h2>
            <div className="platform-grid">
              {platformData.platformUsage.map((platform) => (
                <div key={platform.platform} className="platform-card">
                  <div className="platform-icon">
                    {platform.platform === 'mobile' && <FaMobile />}
                    {platform.platform === 'tablet' && <FaTabletAlt />}
                    {platform.platform === 'desktop' && <FaDesktop />}
                  </div>
                  <div className="platform-content">
                    <h3>{platform.platform}</h3>
                    <div className="platform-stats">
                      <p>Events: {formatNumber(platform.totalEvents)}</p>
                      <p>Users: {formatNumber(platform.uniqueUsers)}</p>
                      <div className="platform-breakdown">
                        <span className="admin-events">Admin: {platform.admin}</span>
                        <span className="user-events">Users: {platform.user}</span>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Event Statistics */}
        {analyticsData?.eventStats && (
          <div className="event-analytics">
            <h2>Event Activity</h2>
            <div className="events-table">
              <table>
                <thead>
                  <tr>
                    <th>Event</th>
                    <th>Total Count</th>
                    <th>Unique Users</th>
                    <th>Avg per User</th>
                  </tr>
                </thead>
                <tbody>
                  {analyticsData.eventStats.map((event) => (
                    <tr key={event.event}>
                      <td className="event-name">
                        <FaEye />
                        {event.event.replace('_', ' ').toUpperCase()}
                      </td>
                      <td>{formatNumber(event.count)}</td>
                      <td>{formatNumber(event.uniqueUsers)}</td>
                      <td>{(event.count / event.uniqueUsers).toFixed(1)}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}

        {/* Daily Trends Chart */}
        {analyticsData?.dailyTrends && (
          <div className="trends-analytics">
            <h2>Daily Activity Trends</h2>
            <div className="trends-chart">
              {analyticsData.dailyTrends.map((day) => (
                <div key={day.date} className="trend-day">
                  <div className="trend-date">{new Date(day.date).toLocaleDateString()}</div>
                  <div className="trend-bars">
                    <div 
                      className="trend-bar admin" 
                      style={{ height: `${(day.admin / Math.max(...analyticsData.dailyTrends.map(d => d.totalEvents))) * 100}%` }}
                      title={`Admin: ${day.admin} events`}
                    ></div>
                    <div 
                      className="trend-bar user" 
                      style={{ height: `${(day.user / Math.max(...analyticsData.dailyTrends.map(d => d.totalEvents))) * 100}%` }}
                      title={`Users: ${day.user} events`}
                    ></div>
                  </div>
                  <div className="trend-total">{day.totalEvents}</div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </Layout>
  );
};

export default Analytics;
