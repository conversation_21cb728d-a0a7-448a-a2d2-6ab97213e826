import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  Alert,
  TextInput,
  Modal,
  FlatList,
  Image,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { mealPlansAPI, mealsAPI, userAPI, aiAPI } from '../../services/api';
import { useAuth } from '../../context/AuthContext';
import { colors, legacyFonts as fonts, legacySpacing as spacing, borderRadius } from '../../styles/theme';
import { commonStyles } from '../../styles/commonStyles';

const CreateMealPlanScreen = ({ navigation, route }) => {
  const { selectedDate, mealType, existingMeal, selectedMeal, allMealsForDate } = route.params || {};

  const [currentDate] = useState(selectedDate || new Date().toISOString().split('T')[0]);
  const [currentMealType, setCurrentMealType] = useState(mealType || 'breakfast');
  const [selectedMeals, setSelectedMeals] = useState({});
  const [availableMeals, setAvailableMeals] = useState([]);
  const [recommendedMeals, setRecommendedMeals] = useState([]);
  const [loading, setLoading] = useState(false);
  const [loadingRecommendations, setLoadingRecommendations] = useState(false);
  const [showMealSelector, setShowMealSelector] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredMeals, setFilteredMeals] = useState([]);
  const [showRecommendations, setShowRecommendations] = useState(true);
  const [userPreferences, setUserPreferences] = useState({});
  const [riceBowls, setRiceBowls] = useState(0);

  // AI Analysis states
  const [mealAnalysis, setMealAnalysis] = useState({});
  const [analysisLoading, setAnalysisLoading] = useState(false);
  const [familyMembers, setFamilyMembers] = useState([]);

  // Validation states
  const [validationResult, setValidationResult] = useState(null);
  const [showValidationModal, setShowValidationModal] = useState(false);
  const [validationLoading, setValidationLoading] = useState(false);

  const { user } = useAuth();
  const mealTypes = ['breakfast', 'lunch', 'dinner', 'snack'];

  useEffect(() => {
    loadAvailableMeals();
    loadUserPreferences();
    loadFamilyMembers();
    // Load existing meal plan using passed data or API call
    loadExistingMealPlan();
    loadExistingRiceBowls();
    loadMealRecommendations();
  }, []);

  useEffect(() => {
    // Load recommendations when meal type changes
    loadMealRecommendations();
  }, [currentMealType]);

  // Load existing meal plan for the selected date
  const loadExistingMealPlan = async () => {
    try {
      console.log('Loading existing meal plan for date:', currentDate);

      // Try to get all meal plans and find the one for our date
      const allMealPlansResponse = await mealPlansAPI.getMealPlans();
      console.log('All meal plans response:', allMealPlansResponse);

      let existingPlan = null;
      if (allMealPlansResponse.data && Array.isArray(allMealPlansResponse.data)) {
        // Find the meal plan for the current date
        existingPlan = allMealPlansResponse.data.find(plan => plan.date === currentDate);
        console.log('Found existing plan for date:', currentDate, existingPlan);
      }

      const existingMeals = {};

      // Initialize empty arrays for all meal types
      mealTypes.forEach(mealType => {
        existingMeals[mealType] = [];
      });

      // If we found an existing plan, extract meals for ALL meal types
      if (existingPlan) {
        mealTypes.forEach(mealType => {
          if (existingPlan[mealType] && Array.isArray(existingPlan[mealType])) {
            existingMeals[mealType] = existingPlan[mealType].map(mealItem => {
              // Handle the nested meal structure from backend
              return mealItem.meal || mealItem;
            });
          }
        });

        console.log('Extracted existing meals for ALL meal types:', existingMeals);
      } else {
        console.log('No existing meal plan found for date:', currentDate);
      }

      // Add any pre-selected meals from navigation
      if (selectedMeal) {
        existingMeals[currentMealType] = [...(existingMeals[currentMealType] || []), selectedMeal];
        console.log('Added selectedMeal to', currentMealType, selectedMeal);
      } else if (existingMeal && existingMeal.meal) {
        // Don't duplicate the existing meal if it's already in the list
        const mealData = existingMeal.meal;
        const mealExists = existingMeals[currentMealType].some(meal =>
          (meal._id || meal.id) === (mealData._id || mealData.id)
        );
        if (!mealExists) {
          existingMeals[currentMealType].push(mealData);
          console.log('Added existingMeal to', currentMealType, mealData);
        }
      }

      setSelectedMeals(existingMeals);
      console.log('Final selected meals state:', existingMeals);

    } catch (error) {
      console.error('Error loading existing meal plan:', error);
      // Initialize empty structure on error
      const emptyMeals = {};
      mealTypes.forEach(mealType => {
        emptyMeals[mealType] = [];
      });

      // Add any pre-selected meals
      if (selectedMeal) {
        emptyMeals[currentMealType] = [selectedMeal];
      } else if (existingMeal && existingMeal.meal) {
        emptyMeals[currentMealType] = [existingMeal.meal];
      }

      setSelectedMeals(emptyMeals);
    }
  };

  const loadExistingRiceBowls = async () => {
    try {
      if (currentDate) {
        const response = await mealPlansAPI.getMealPlanByDate(currentDate);
        if (response && response.riceBowls !== undefined) {
          setRiceBowls(response.riceBowls);
        }
      }
    } catch (error) {
      console.error('Error loading existing rice bowls:', error);
      // Default to 0 if error
      setRiceBowls(0);
    }
  };

  useEffect(() => {
    filterMeals();
  }, [availableMeals, searchQuery, userPreferences, currentMealType]);

  // Trigger AI analysis when filtered meals change
  useEffect(() => {
    if (filteredMeals.length > 0 && familyMembers.length >= 0) {
      fetchMealAnalysis(filteredMeals);
    }
  }, [filteredMeals, familyMembers, userPreferences]);

  const loadUserPreferences = async () => {
    try {
      if (user) {
        const response = await userAPI.getDietaryPreferences();
        console.log('Loaded user preferences in CreateMealPlan:', response);
        if (response.data && response.data.success && response.data.dietaryPreferences) {
          setUserPreferences(response.data.dietaryPreferences);
        } else if (response.success && response.dietaryPreferences) {
          setUserPreferences(response.dietaryPreferences);
        }
      }
    } catch (error) {
      console.error('Error loading user preferences:', error);
      setUserPreferences({});
    }
  };

  const loadFamilyMembers = async () => {
    try {
      if (user) {
        const response = await userAPI.getFamilyMembers();
        console.log('Loaded family members in CreateMealPlan:', response);
        setFamilyMembers(response.data || []);
      }
    } catch (error) {
      console.error('Error loading family members:', error);
      setFamilyMembers([]);
    }
  };

  const loadAvailableMeals = async () => {
    try {
      setLoading(true);
      const response = await mealsAPI.getFilipinoDishes();
      setAvailableMeals(response.data || []);
    } catch (error) {
      console.error('Error loading meals:', error);
      Alert.alert('Error', 'Failed to load available meals');
    } finally {
      setLoading(false);
    }
  };

  const filterMeals = () => {
    let filtered = availableMeals;

    console.log('🔍 Filtering CreateMealPlan meals with preferences:', userPreferences);
    console.log('📊 Total available meals before filtering:', availableMeals.length);

    // Filter by current meal type first
    filtered = filtered.filter(meal => {
      if (!meal.mealType || meal.mealType.length === 0) return false;
      return meal.mealType.includes(currentMealType);
    });

    console.log(`📊 Meals after filtering by meal type (${currentMealType}):`, filtered.length);

    // Filter by user dietary preferences
    if (userPreferences && Object.keys(userPreferences).length > 0) {
      filtered = filtered.filter(meal => {
        const dietType = meal.dietType || meal.dietaryAttributes || {};

        // Check dietary restrictions
        if (userPreferences.restrictions && userPreferences.restrictions.length > 0) {
          const meetsRestrictions = userPreferences.restrictions.every(restriction => {
            let meets = false;
            switch(restriction) {
              case 'Vegetarian':
                meets = dietType.isVegetarian;
                break;
              case 'Vegan':
                meets = dietType.isVegan;
                break;
              case 'Gluten-Free':
                meets = dietType.isGlutenFree;
                break;
              case 'Dairy-Free':
                meets = dietType.isDairyFree;
                break;
              case 'Nut-Free':
                meets = dietType.isNutFree;
                break;
              case 'Low-Carb':
                meets = dietType.isLowCarb;
                break;
              case 'Keto':
                meets = dietType.isKeto;
                break;
              case 'Pescatarian':
                meets = dietType.isPescatarian;
                break;
              case 'Halal':
                meets = dietType.isHalal;
                break;
              default:
                meets = true;
            }

            return meets;
          });
          if (!meetsRestrictions) return false;
        }

        // Check allergies - exclude meals with allergens
        if (userPreferences.allergies && userPreferences.allergies.length > 0) {
          const ingredients = meal.ingredients || [];
          const hasAllergen = userPreferences.allergies.some(allergy =>
            ingredients.some(ingredient =>
              ingredient.toLowerCase().includes(allergy.toLowerCase())
            )
          );
          if (hasAllergen) {
            console.log(`❌ CreateMealPlan: Meal "${meal.name}" contains allergen "${userPreferences.allergies.join(', ')}" in ingredients: ${ingredients.join(', ')}`);
            return false;
          }
        }

        return true;
      });
    }

    // Filter by search query
    if (searchQuery.trim()) {
      filtered = filtered.filter(meal =>
        meal.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (meal.description && meal.description.toLowerCase().includes(searchQuery.toLowerCase()))
      );
    }

    console.log('✅ CreateMealPlan: Meals after filtering:', filtered.length);
    if (filtered.length > 0) {
      console.log('📋 CreateMealPlan: Sample filtered meals:', filtered.slice(0, 3).map(m => m.name));
    }

    setFilteredMeals(filtered);
  };

  const loadMealRecommendations = async () => {
    try {
      setLoadingRecommendations(true);
      const response = await mealsAPI.getMealRecommendations({
        mealType: currentMealType,
        includeFamily: 'false'
      });

      console.log('Meal recommendations response:', response);
      setRecommendedMeals(response.data || []);
    } catch (error) {
      console.error('Error loading meal recommendations:', error);
      // Don't show error to user, just use empty recommendations
      setRecommendedMeals([]);
    } finally {
      setLoadingRecommendations(false);
    }
  };

  // AI analysis with 5-meal batches and lazy loading for mobile
  const fetchMealAnalysis = async (mealsToAnalyze) => {
    if (!mealsToAnalyze || mealsToAnalyze.length === 0) return;

    try {
      setAnalysisLoading(true);
      console.log('🚀 Mobile: Starting lazy loading AI analysis for', mealsToAnalyze.length, 'meals in batches of 5');

      // Start with empty analysis map
      setMealAnalysis({});

      const batchSize = 5; // 5-meal batches for mobile

      // Process meals in batches of 5 with lazy loading
      for (let i = 0; i < mealsToAnalyze.length; i += batchSize) {
        const batch = mealsToAnalyze.slice(i, i + batchSize);
        const batchNumber = Math.floor(i / batchSize) + 1;
        const totalBatches = Math.ceil(mealsToAnalyze.length / batchSize);

        console.log(`📊 Mobile: Lazy loading batch ${batchNumber}/${totalBatches}: ${batch.length} meals`);

        try {
          const response = await aiAPI.analyzeMealsForFamily({
            meals: batch,
            mealType: currentMealType
          });

          // Handle nested response structure: response.analysis.analysis.mealAnalysis
          const mealAnalysisData = response.analysis?.analysis?.mealAnalysis || response.analysis?.mealAnalysis;

          if (response.success && mealAnalysisData && Array.isArray(mealAnalysisData)) {
            // IMMEDIATELY update UI with new results (lazy loading)
            setMealAnalysis(prevAnalysis => {
              const newAnalysis = { ...prevAnalysis };
              mealAnalysisData.forEach(analysis => {
                const mealId = analysis.mealId || analysis.mealName;
                newAnalysis[mealId] = analysis;
              });
              console.log(`✅ Mobile: Batch ${batchNumber} loaded: ${batch.length} meals now visible`);
              return newAnalysis;
            });
          } else {
            console.error(`❌ Mobile: Invalid response for batch ${batchNumber}:`, response);
          }
        } catch (batchError) {
          console.error(`❌ Mobile: Error analyzing batch ${batchNumber}:`, batchError);
          // Continue with next batch even if one fails
        }

        // Very small delay for smooth lazy loading on mobile
        if (i + batchSize < mealsToAnalyze.length) {
          await new Promise(resolve => setTimeout(resolve, 100));
        }
      }

      console.log('✅ Mobile: All lazy loading batches completed');

    } catch (error) {
      console.error('❌ Mobile: Error fetching meal analysis:', error);
      setMealAnalysis({});
    } finally {
      setAnalysisLoading(false);
    }
  };

  // Get compatibility status for a meal with lazy loading states
  const getMealCompatibility = (meal) => {
    const mealId = meal._id || meal.id || meal.name;
    const analysis = mealAnalysis[mealId];

    if (!analysis) {
      return {
        overallCompatibility: 'pending',
        familyCompatibility: [],
        loading: analysisLoading,
        pending: analysisLoading // Show pending state during lazy loading
      };
    }

    return {
      overallCompatibility: analysis.overallCompatibility,
      familyCompatibility: analysis.familyCompatibility,
      generalNotes: analysis.generalNotes,
      suggestions: analysis.suggestions,
      loading: false,
      pending: false
    };
  };



  const handleMealSelect = (meal) => {
    const currentMeals = selectedMeals[currentMealType] || [];
    const isSelected = currentMeals.some(m => (m.id || m._id) === (meal.id || meal._id));

    if (isSelected) {
      // Remove meal
      setSelectedMeals(prev => ({
        ...prev,
        [currentMealType]: currentMeals.filter(m => (m.id || m._id) !== (meal.id || meal._id))
      }));
    } else {
      // Add meal
      setSelectedMeals(prev => ({
        ...prev,
        [currentMealType]: [...currentMeals, meal]
      }));
    }
  };

  const handleMealPress = (meal) => {
    // Navigate to meal detail - tracking will be handled by MealDetailScreen
    navigation.navigate('MealDetail', { meal });
  };

  const handleSaveMealPlan = async () => {
    try {
      setLoading(true);

      // Check if any meals are selected
      const hasSelectedMeals = Object.values(selectedMeals).some(meals => meals && meals.length > 0);

      if (!hasSelectedMeals) {
        Alert.alert('No Meals Selected', 'Please select at least one meal before saving.');
        return;
      }

      // Prepare selected meals for validation
      const allSelectedMeals = [];
      for (const [mealType, meals] of Object.entries(selectedMeals)) {
        if (meals && meals.length > 0) {
          allSelectedMeals.push(...meals);
        }
      }

      console.log('🔍 Starting meal plan validation...');
      setValidationLoading(true);

      // Call validation API
      const validationResponse = await mealPlansAPI.validateMealPlan(allSelectedMeals);

      setValidationLoading(false);

      if (validationResponse.data.success && validationResponse.data.validation) {
        const validation = validationResponse.data.validation;
        setValidationResult(validation);

        // Show validation results modal
        setShowValidationModal(true);
        setLoading(false);
        return; // Stop here to show validation results
      } else {
        console.warn('Validation failed, proceeding without validation');
        // Continue with save if validation fails
      }

      // If validation passes or fails, continue with save
      await proceedWithSave();

    } catch (error) {
      console.error('Error in meal plan save process:', error);
      setValidationLoading(false);
      Alert.alert('Error', `Failed to process meal plan: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const proceedWithSave = async () => {
    try {
      setLoading(true);

      // Prepare meal plan data in the format expected by the backend saveMealPlan endpoint
      const mealPlanData = {
        name: `Meal Plan for ${new Date(currentDate).toLocaleDateString()}`,
        startDate: currentDate,
        endDate: currentDate,
        dietaryPreference: 'all', // Default value
        riceBowls: riceBowls, // Include rice bowls
        meals: [],
        mealTimes: {
          breakfast: '08:00',
          lunch: '12:00',
          dinner: '18:00',
          snack: '15:00' // Add snack time
        }
      };

      // Convert selected meals to the format expected by the backend
      // The backend expects: { date, mealType, meal: mealId, mealData: fullMealObject }
      for (const [mealType, meals] of Object.entries(selectedMeals)) {
        if (meals && meals.length > 0) {
          for (const meal of meals) {
            try {
              // Create meal data with instanceId for uniqueness
              const instanceId = `${meal.name}-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;

              mealPlanData.meals.push({
                date: currentDate,
                mealType: mealType,
                meal: meal._id || meal.id || null, // Backend meal ID if available
                mealData: {
                  name: meal.name,
                  calories: meal.calories || 0,
                  protein: meal.protein || 0,
                  carbs: meal.carbs || 0,
                  fat: meal.fat || 0,
                  category: Array.isArray(meal.category) ? meal.category : [meal.category || 'General'],
                  description: meal.description || '',
                  image: meal.image || '',
                  ingredients: meal.ingredients || [],
                  instructions: meal.instructions || [],
                  dietaryTags: meal.dietaryTags || [],
                  rating: meal.rating || 0,
                  instanceId: instanceId
                }
              });

              console.log(`Added ${meal.name} to ${mealType} for ${currentDate}`);
            } catch (error) {
              console.error(`Error processing ${mealType} meal:`, error);
              Alert.alert(
                'Meal Processing Error',
                `Failed to process "${meal.name}". Please try again.`,
                [{ text: 'OK' }]
              );
            }
          }
        }
      }

      if (mealPlanData.meals.length === 0) {
        Alert.alert('No Valid Meals', 'No meals could be processed. Please check your selections and try again.');
        return;
      }

      console.log('Saving meal plan with data:', mealPlanData);

      // Use the saveMealPlan endpoint
      const response = await mealPlansAPI.saveMealPlan(mealPlanData);

      console.log('Meal plan save response:', response);

      // Track meal additions for history
      for (const mealEntry of mealPlanData.meals) {
        try {
          await userAPI.addRecentlyAddedToMealPlan({
            meal: { name: mealEntry.mealData.name, _id: mealEntry.mealData.instanceId },
            addedToDate: mealEntry.date,
            addedToMealType: mealEntry.mealType
          });
        } catch (trackingError) {
          console.error('Error tracking meal addition:', trackingError);
          // Don't fail the main operation if tracking fails
        }
      }

      // Close validation modal if open
      setShowValidationModal(false);
      setValidationResult(null);

      Alert.alert(
        'Success',
        'Meal plan saved successfully!',
        [
          {
            text: 'OK',
            onPress: () => navigation.goBack()
          }
        ]
      );
    } catch (error) {
      console.error('Error saving meal plan:', error);
      console.error('Error details:', error.response?.data);
      Alert.alert('Error', `Failed to save meal plan: ${error.response?.data?.message || error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const renderMealTypeSelector = () => (
    <View style={styles.mealTypeSelector}>
      <Text style={styles.sectionTitle}>Select Meal Type</Text>
      <View style={styles.mealTypeButtons}>
        {mealTypes.map((type) => (
          <TouchableOpacity
            key={type}
            style={[
              styles.mealTypeButton,
              currentMealType === type && styles.mealTypeButtonActive
            ]}
            onPress={() => setCurrentMealType(type)}
          >
            <Text style={[
              styles.mealTypeButtonText,
              currentMealType === type && styles.mealTypeButtonTextActive
            ]}>
              {type.charAt(0).toUpperCase() + type.slice(1)}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  const renderSelectedMeals = () => {
    const meals = selectedMeals[currentMealType] || [];

    return (
      <View style={styles.selectedMealsSection}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>
            Selected {currentMealType.charAt(0).toUpperCase() + currentMealType.slice(1)} Meals
          </Text>
          <TouchableOpacity
            style={styles.addMealButton}
            onPress={() => setShowMealSelector(true)}
          >
            <Ionicons name="add-circle-outline" size={24} color={colors.primary} />
            <Text style={styles.addMealText}>Add Meal</Text>
          </TouchableOpacity>
        </View>

        {meals.length > 0 ? (
          <View style={styles.mealsListContainer}>
            {meals.map((meal, index) => (
              <View key={(meal.id || meal._id || index).toString()} style={styles.selectedMealCard}>
                <Image
                  source={{ uri: meal.image || 'https://via.placeholder.com/100x75' }}
                  style={styles.selectedMealImage}
                  resizeMode="cover"
                />
                <View style={styles.selectedMealInfo}>
                  <Text style={styles.selectedMealName} numberOfLines={1}>
                    {meal.name}
                  </Text>

                  {/* Description */}
                  {meal.description && (
                    <Text style={styles.selectedMealDescription} numberOfLines={1}>
                      {meal.description}
                    </Text>
                  )}

                  {/* Nutrition Summary */}
                  <View style={styles.selectedNutritionSummary}>
                    <Text style={styles.selectedNutritionText}>
                      {meal.calories || 0} cal
                    </Text>
                    {meal.protein && (
                      <Text style={styles.selectedNutritionText}>
                        • {meal.protein}g protein
                      </Text>
                    )}
                    {meal.carbs && (
                      <Text style={styles.selectedNutritionText}>
                        • {meal.carbs}g carbs
                      </Text>
                    )}
                  </View>

                  {/* Category and Rating */}
                  <View style={styles.selectedMealMeta}>
                    {meal.category && (
                      <View style={styles.selectedCategoryTag}>
                        <Text style={styles.selectedCategoryText}>
                          {Array.isArray(meal.category) ? meal.category[0] : meal.category}
                        </Text>
                      </View>
                    )}
                    {meal.rating && (
                      <View style={styles.selectedRatingContainer}>
                        <Ionicons name="star" size={12} color="#FFD700" />
                        <Text style={styles.selectedRatingText}>{meal.rating}</Text>
                      </View>
                    )}
                  </View>
                </View>
                <TouchableOpacity
                  style={styles.removeMealButton}
                  onPress={() => handleMealSelect(meal)}
                >
                  <Ionicons name="close-circle" size={24} color={colors.secondary} />
                </TouchableOpacity>
              </View>
            ))}
          </View>
        ) : (
          <View style={styles.emptyMealsContainer}>
            <Ionicons name="restaurant-outline" size={48} color={colors.textSecondary} />
            <Text style={styles.emptyMealsText}>
              No {currentMealType} meals selected
            </Text>
            <Text style={styles.emptyMealsSubtext}>
              Tap "Add Meal" to choose from Filipino dishes
            </Text>
          </View>
        )}
      </View>
    );
  };

  const renderMealSelectorModal = () => (
    <Modal
      visible={showMealSelector}
      animationType="slide"
      presentationStyle="pageSheet"
    >
      <SafeAreaView style={styles.modalContainer}>
        {/* Modal Header */}
        <View style={styles.modalHeader}>
          <TouchableOpacity onPress={() => setShowMealSelector(false)}>
            <Text style={styles.modalCancelText}>Cancel</Text>
          </TouchableOpacity>
          <Text style={styles.modalTitle}>
            Select {currentMealType.charAt(0).toUpperCase() + currentMealType.slice(1)} Meals
          </Text>
          <TouchableOpacity onPress={() => setShowMealSelector(false)}>
            <Text style={styles.modalDoneText}>Done</Text>
          </TouchableOpacity>
        </View>

        {/* Search Bar */}
        <View style={styles.searchContainer}>
          <View style={styles.searchInputContainer}>
            <Ionicons name="search" size={20} color={colors.textSecondary} />
            <TextInput
              style={styles.searchInput}
              placeholder="Search Filipino dishes..."
              value={searchQuery}
              onChangeText={setSearchQuery}
              placeholderTextColor={colors.textSecondary}
            />
            {searchQuery.length > 0 && (
              <TouchableOpacity onPress={() => setSearchQuery('')}>
                <Ionicons name="close-circle" size={20} color={colors.textSecondary} />
              </TouchableOpacity>
            )}
          </View>
        </View>

        {/* Recommendations Section */}
        {showRecommendations && recommendedMeals.length > 0 && !searchQuery.trim() && (
          <View style={styles.recommendationsSection}>
            <View style={styles.recommendationsHeader}>
              <Ionicons name="bulb-outline" size={20} color={colors.primary} />
              <Text style={styles.recommendationsTitle}>Recommended for You</Text>
              <TouchableOpacity onPress={() => setShowRecommendations(false)}>
                <Ionicons name="close" size={20} color={colors.textSecondary} />
              </TouchableOpacity>
            </View>
            <ScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={styles.recommendationsList}
            >
              {recommendedMeals.slice(0, 5).map((meal, index) => {
                const isSelected = (selectedMeals[currentMealType] || [])
                  .some(m => (m.id || m._id) === (meal.id || meal._id));

                return (
                  <View
                    key={(meal.id || meal._id || index).toString()}
                    style={[styles.recommendationCard, isSelected && styles.recommendationCardSelected]}
                  >
                    <TouchableOpacity
                      style={styles.recommendationImageContainer}
                      onPress={() => handleMealPress(meal)}
                    >
                      <Image
                        source={{ uri: meal.image || 'https://via.placeholder.com/120x90' }}
                        style={styles.recommendationImage}
                        resizeMode="cover"
                      />
                      <View style={styles.detailsOverlay}>
                        <Ionicons name="eye-outline" size={16} color={colors.surface} />
                      </View>
                    </TouchableOpacity>
                    <View style={styles.recommendationInfo}>
                      <Text style={styles.recommendationName} numberOfLines={2}>
                        {meal.name}
                      </Text>
                      <Text style={styles.recommendationCalories}>
                        {meal.calories || 0} cal
                      </Text>
                      {meal.recommendationReasons && meal.recommendationReasons.length > 0 && (
                        <Text style={styles.recommendationReason} numberOfLines={1}>
                          {meal.recommendationReasons[0]}
                        </Text>
                      )}
                    </View>
                    <TouchableOpacity
                      style={styles.recommendationAction}
                      onPress={() => handleMealSelect(meal)}
                    >
                      <Ionicons
                        name={isSelected ? "checkmark-circle" : "add-circle-outline"}
                        size={24}
                        color={isSelected ? colors.primary : colors.textSecondary}
                      />
                    </TouchableOpacity>
                  </View>
                );
              })}
            </ScrollView>
          </View>
        )}

        {/* Meals List */}
        <FlatList
          data={filteredMeals}
          renderItem={({ item: meal }) => {
            const isSelected = (selectedMeals[currentMealType] || [])
              .some(m => (m.id || m._id) === (meal.id || meal._id));

            return (
              <View
                style={[styles.mealSelectorCard, isSelected && styles.mealSelectorCardSelected]}
              >
                <TouchableOpacity
                  style={styles.mealImageContainer}
                  onPress={() => handleMealPress(meal)}
                >
                  <Image
                    source={{ uri: meal.image || 'https://via.placeholder.com/120x90' }}
                    style={styles.mealSelectorImage}
                    resizeMode="cover"
                  />
                  <View style={styles.detailsOverlay}>
                    <Ionicons name="eye-outline" size={16} color={colors.surface} />
                    <Text style={styles.detailsText}>Details</Text>
                  </View>
                </TouchableOpacity>
                <View style={styles.mealSelectorInfo}>
                  <Text style={styles.mealSelectorName} numberOfLines={2}>
                    {meal.name}
                  </Text>

                  {/* Description */}
                  {meal.description && (
                    <Text style={styles.mealDescription} numberOfLines={2}>
                      {meal.description}
                    </Text>
                  )}

                  {/* Nutrition Info */}
                  <View style={styles.nutritionInfo}>
                    <View style={styles.nutritionItem}>
                      <Text style={styles.nutritionLabel}>Calories</Text>
                      <Text style={styles.nutritionValue}>{meal.calories || 0}</Text>
                    </View>
                    {meal.protein && (
                      <View style={styles.nutritionItem}>
                        <Text style={styles.nutritionLabel}>Protein</Text>
                        <Text style={styles.nutritionValue}>{meal.protein}g</Text>
                      </View>
                    )}
                    {meal.carbs && (
                      <View style={styles.nutritionItem}>
                        <Text style={styles.nutritionLabel}>Carbs</Text>
                        <Text style={styles.nutritionValue}>{meal.carbs}g</Text>
                      </View>
                    )}
                    {meal.fat && (
                      <View style={styles.nutritionItem}>
                        <Text style={styles.nutritionLabel}>Fat</Text>
                        <Text style={styles.nutritionValue}>{meal.fat}g</Text>
                      </View>
                    )}
                  </View>

                  {/* Meta Info */}
                  <View style={styles.mealSelectorMeta}>
                    {meal.category && (
                      <View style={styles.categoryTag}>
                        <Text style={styles.categoryTagText}>
                          {Array.isArray(meal.category) ? meal.category[0] : meal.category}
                        </Text>
                      </View>
                    )}
                    {meal.rating && (
                      <View style={styles.ratingContainer}>
                        <Ionicons name="star" size={14} color="#FFD700" />
                        <Text style={styles.ratingText}>{meal.rating}</Text>
                      </View>
                    )}
                    {meal.prepTime && (
                      <View style={styles.timeContainer}>
                        <Ionicons name="time-outline" size={14} color={colors.textSecondary} />
                        <Text style={styles.timeText}>{meal.prepTime}min</Text>
                      </View>
                    )}
                  </View>

                  {/* Dietary Tags */}
                  {meal.dietaryTags && meal.dietaryTags.length > 0 && (
                    <View style={styles.dietaryTags}>
                      {meal.dietaryTags.slice(0, 3).map((tag, index) => (
                        <View key={index} style={styles.dietaryTag}>
                          <Text style={styles.dietaryTagText}>{tag}</Text>
                        </View>
                      ))}
                    </View>
                  )}

                  {/* Dietary Attributes Badges */}
                  {(meal.dietType || meal.dietaryAttributes) && (
                    <View style={styles.dietaryBadges}>
                      {(meal.dietType?.isVegetarian || meal.dietaryAttributes?.isVegetarian) && (
                        <View style={[styles.dietaryBadge, styles.vegetarianBadge]}>
                          <Text style={styles.dietaryBadgeText}>🌱 Vegetarian</Text>
                        </View>
                      )}
                      {(meal.dietType?.isVegan || meal.dietaryAttributes?.isVegan) && (
                        <View style={[styles.dietaryBadge, styles.veganBadge]}>
                          <Text style={styles.dietaryBadgeText}>🌿 Vegan</Text>
                        </View>
                      )}
                      {(meal.dietType?.isGlutenFree || meal.dietaryAttributes?.isGlutenFree) && (
                        <View style={[styles.dietaryBadge, styles.glutenFreeBadge]}>
                          <Text style={styles.dietaryBadgeText}>🌾 Gluten-Free</Text>
                        </View>
                      )}
                      {(meal.dietType?.isDairyFree || meal.dietaryAttributes?.isDairyFree) && (
                        <View style={[styles.dietaryBadge, styles.dairyFreeBadge]}>
                          <Text style={styles.dietaryBadgeText}>🥛 Dairy-Free</Text>
                        </View>
                      )}
                      {(meal.dietType?.isNutFree || meal.dietaryAttributes?.isNutFree) && (
                        <View style={[styles.dietaryBadge, styles.nutFreeBadge]}>
                          <Text style={styles.dietaryBadgeText}>🥜 Nut-Free</Text>
                        </View>
                      )}
                      {(meal.dietType?.isLowCarb || meal.dietaryAttributes?.isLowCarb) && (
                        <View style={[styles.dietaryBadge, styles.lowCarbBadge]}>
                          <Text style={styles.dietaryBadgeText}>⚡ Low-Carb</Text>
                        </View>
                      )}
                      {meal.dietType?.isKeto && (
                        <View style={[styles.dietaryBadge, styles.ketoBadge]}>
                          <Text style={styles.dietaryBadgeText}>🔥 Keto</Text>
                        </View>
                      )}
                      {meal.dietType?.isPescatarian && (
                        <View style={[styles.dietaryBadge, styles.pescatarianBadge]}>
                          <Text style={styles.dietaryBadgeText}>🐟 Pescatarian</Text>
                        </View>
                      )}
                      {meal.dietType?.isHalal && (
                        <View style={[styles.dietaryBadge, styles.halalBadge]}>
                          <Text style={styles.dietaryBadgeText}>🕌 Halal</Text>
                        </View>
                      )}
                    </View>
                  )}

                  {/* AI Compatibility Analysis */}
                  {(() => {
                    const compatibility = getMealCompatibility(meal);

                    // Show pending state during lazy loading
                    if (compatibility.pending && !compatibility.familyCompatibility.length) {
                      return (
                        <View style={styles.mealCompatibilityPending}>
                          <Text style={styles.pendingText}>🤖 Analyzing...</Text>
                        </View>
                      );
                    }

                    // Show results as they come in (lazy loading)
                    if (compatibility.familyCompatibility.length > 0) {
                      return (
                        <View style={styles.mealCompatibility}>
                          <View style={styles.compatibilityHeader}>
                            <Text style={styles.compatibilityTitle}>Family Compatibility</Text>
                            <View style={[styles.compatibilityBadge]}>
                              <Text style={styles.compatibilityBadgeText}>
                                {(() => {
                                  const status = compatibility.overallCompatibility;
                                  const emoji = status === 'excellent' ? '🟢' :
                                               status === 'good' ? '🟡' :
                                               status === 'fair' ? '🟠' :
                                               status === 'poor' ? '🔴' :
                                               status === 'pending' ? '⏳' : '⚪';
                                  return emoji + ' ' + status.charAt(0).toUpperCase() + status.slice(1);
                                })()}
                              </Text>
                            </View>
                          </View>
                          <View style={styles.familyCompatibilityList}>
                            {compatibility.familyCompatibility.map((member, index) => (
                              <View key={index} style={styles.memberCompatibility}>
                                <Text style={styles.memberName}>{member.memberName}:</Text>
                                <Text style={[styles.memberConcern, member.compatible ? styles.memberConcernGood : styles.memberConcernBad]}>
                                  {member.compatible ? '✅ ' : '❌ '}{member.reasons && member.reasons.length > 0 ? member.reasons[0] : (member.compatible ? 'All is well' : 'Issue found')}
                                </Text>
                              </View>
                            ))}
                          </View>
                        </View>
                      );
                    }

                    // Show nothing if no analysis yet and not loading
                    return null;
                  })()}
                </View>
                <TouchableOpacity
                  style={styles.mealSelectorAction}
                  onPress={() => handleMealSelect(meal)}
                >
                  <Ionicons
                    name={isSelected ? "checkmark-circle" : "add-circle-outline"}
                    size={28}
                    color={isSelected ? colors.primary : colors.textSecondary}
                  />
                </TouchableOpacity>
              </View>
            );
          }}
          keyExtractor={(item, index) => (item.id || item._id || index).toString()}
          contentContainerStyle={styles.mealsList}
          ListEmptyComponent={
            <View style={commonStyles.emptyContainer}>
              <Ionicons name="restaurant-outline" size={64} color={colors.textSecondary} />
              <Text style={commonStyles.emptyTitle}>No meals found</Text>
              <Text style={commonStyles.emptySubtitle}>
                Try adjusting your search terms
              </Text>
            </View>
          }
        />
      </SafeAreaView>
    </Modal>
  );

  return (
    <SafeAreaView style={commonStyles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Ionicons name="arrow-back" size={24} color={colors.surface} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Create Meal Plan</Text>
        <TouchableOpacity onPress={handleSaveMealPlan} disabled={loading}>
          <Text style={[styles.saveButton, loading && styles.saveButtonDisabled]}>
            {loading ? 'Saving...' : 'Save'}
          </Text>
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.container}>
        {/* Date Display */}
        <View style={styles.dateSection}>
          <Text style={styles.dateLabel}>Planning for:</Text>
          <Text style={styles.dateText}>
            {new Date(currentDate).toLocaleDateString('en-US', {
              weekday: 'long',
              year: 'numeric',
              month: 'long',
              day: 'numeric',
            })}
          </Text>
        </View>

        {/* Meal Type Selector */}
        {renderMealTypeSelector()}

        {/* Selected Meals */}
        {renderSelectedMeals()}

        {/* Rice Bowls Section */}
        <View style={styles.riceBowlsSection}>
          <Text style={styles.sectionTitle}>Rice Bowls</Text>
          <Text style={styles.riceBowlsDescription}>
            How many bowls of rice would you like with your meal plan?
          </Text>
          <View style={styles.riceBowlsInputContainer}>
            <TouchableOpacity
              style={styles.riceBowlsButton}
              onPress={() => setRiceBowls(Math.max(0, riceBowls - 1))}
            >
              <Ionicons name="remove" size={24} color={colors.primary} />
            </TouchableOpacity>
            <View style={styles.riceBowlsInputWrapper}>
              <TextInput
                style={styles.riceBowlsInput}
                value={riceBowls.toString()}
                onChangeText={(text) => {
                  const num = parseInt(text) || 0;
                  setRiceBowls(Math.max(0, num));
                }}
                keyboardType="numeric"
                placeholder="0"
              />
            </View>
            <TouchableOpacity
              style={styles.riceBowlsButton}
              onPress={() => setRiceBowls(riceBowls + 1)}
            >
              <Ionicons name="add" size={24} color={colors.primary} />
            </TouchableOpacity>
          </View>
        </View>

        {/* Summary */}
        <View style={styles.summarySection}>
          <Text style={styles.sectionTitle}>Meal Plan Summary</Text>
          {mealTypes.map(type => {
            const meals = selectedMeals[type] || [];
            const totalCalories = meals.reduce((sum, meal) => sum + (meal.calories || 0), 0);

            return (
              <View key={type} style={styles.summaryRow}>
                <Text style={styles.summaryMealType}>
                  {type.charAt(0).toUpperCase() + type.slice(1)}:
                </Text>
                <Text style={styles.summaryCount}>
                  {meals.length} meal{meals.length !== 1 ? 's' : ''} ({totalCalories} cal)
                </Text>
              </View>
            );
          })}
          {riceBowls > 0 && (
            <View style={styles.summaryRow}>
              <Text style={styles.summaryMealType}>Rice:</Text>
              <Text style={styles.summaryCount}>
                {riceBowls} bowl{riceBowls !== 1 ? 's' : ''}
              </Text>
            </View>
          )}
        </View>
      </ScrollView>

      {/* Meal Selector Modal */}
      {renderMealSelectorModal()}

      {/* Validation Modal */}
      <Modal
        visible={showValidationModal}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setShowValidationModal(false)}
      >
        <View style={styles.validationModalOverlay}>
          <View style={styles.validationModalContent}>
            <View style={styles.validationModalHeader}>
              <Text style={styles.validationModalTitle}>Meal Plan Analysis</Text>
              <TouchableOpacity
                onPress={() => setShowValidationModal(false)}
                style={styles.validationModalCloseButton}
              >
                <Ionicons name="close" size={24} color={colors.text} />
              </TouchableOpacity>
            </View>

            {validationLoading ? (
              <View style={styles.validationLoadingContainer}>
                <Text style={styles.validationLoadingText}>🤖 Analyzing your meal plan...</Text>
              </View>
            ) : validationResult ? (
              <ScrollView style={styles.validationResultsContainer}>
                <Text style={styles.validationSummaryText}>
                  Total Calories: {validationResult.totalCalories} kcal
                </Text>

                <View style={styles.validationMembersContainer}>
                  {validationResult.memberValidations.map((member, index) => (
                    <View key={index} style={styles.validationMemberCard}>
                      <View style={styles.validationMemberHeader}>
                        <Text style={styles.validationMemberName}>{member.memberName}</Text>
                        <View style={[
                          styles.validationStatusBadge,
                          member.concern === 'All is well'
                            ? styles.validationStatusGood
                            : styles.validationStatusWarning
                        ]}>
                          <Text style={styles.validationStatusText}>
                            {member.concern === 'All is well' ? '✓' : '⚠️'}
                          </Text>
                        </View>
                      </View>
                      <Text style={[
                        styles.validationConcernText,
                        member.concern === 'All is well'
                          ? styles.validationConcernGood
                          : styles.validationConcernWarning
                      ]}>
                        {member.concern}
                      </Text>
                    </View>
                  ))}
                </View>
              </ScrollView>
            ) : null}

            <View style={styles.validationModalActions}>
              {validationResult?.hasIssues ? (
                <>
                  <TouchableOpacity
                    style={[styles.validationActionButton, styles.validationSecondaryButton]}
                    onPress={() => setShowValidationModal(false)}
                  >
                    <Text style={styles.validationSecondaryButtonText}>Change Meals</Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={[styles.validationActionButton, styles.validationPrimaryButton]}
                    onPress={proceedWithSave}
                    disabled={loading}
                  >
                    <Text style={styles.validationPrimaryButtonText}>
                      {loading ? 'Creating...' : 'Create Meal Plan Anyway'}
                    </Text>
                  </TouchableOpacity>
                </>
              ) : (
                <>
                  <TouchableOpacity
                    style={[styles.validationActionButton, styles.validationSecondaryButton]}
                    onPress={() => setShowValidationModal(false)}
                  >
                    <Text style={styles.validationSecondaryButtonText}>Cancel</Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={[styles.validationActionButton, styles.validationPrimaryButton]}
                    onPress={proceedWithSave}
                    disabled={loading}
                  >
                    <Text style={styles.validationPrimaryButtonText}>
                      {loading ? 'Creating...' : 'Create Meal Plan'}
                    </Text>
                  </TouchableOpacity>
                </>
              )}
            </View>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    backgroundColor: colors.primary,
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.md,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  headerTitle: {
    fontSize: fonts.sizes.large,
    fontWeight: 'bold',
    color: colors.surface,
  },
  saveButton: {
    fontSize: fonts.sizes.medium,
    fontWeight: 'bold',
    color: colors.surface,
  },
  saveButtonDisabled: {
    opacity: 0.6,
  },
  dateSection: {
    backgroundColor: colors.surface,
    padding: spacing.md,
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  dateLabel: {
    fontSize: fonts.sizes.medium,
    color: colors.textSecondary,
    marginBottom: spacing.xs,
  },
  dateText: {
    fontSize: fonts.sizes.large,
    fontWeight: 'bold',
    color: colors.text,
    textAlign: 'center',
  },
  mealTypeSelector: {
    backgroundColor: colors.surface,
    padding: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  sectionTitle: {
    fontSize: fonts.sizes.medium,
    fontWeight: 'bold',
    color: colors.text,
    marginBottom: spacing.md,
  },
  mealTypeButtons: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.sm,
  },
  mealTypeButton: {
    backgroundColor: colors.background,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: borderRadius.medium,
    borderWidth: 1,
    borderColor: colors.border,
  },
  mealTypeButtonActive: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  mealTypeButtonText: {
    fontSize: fonts.sizes.medium,
    color: colors.text,
    fontWeight: '500',
  },
  mealTypeButtonTextActive: {
    color: colors.surface,
  },
  selectedMealsSection: {
    backgroundColor: colors.surface,
    padding: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  addMealButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.background,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: borderRadius.medium,
    borderWidth: 1,
    borderColor: colors.primary,
  },
  addMealText: {
    fontSize: fonts.sizes.small,
    color: colors.primary,
    marginLeft: spacing.xs,
    fontWeight: '500',
  },
  mealsListContainer: {
    maxHeight: 200,
  },
  selectedMealCard: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: colors.surface,
    borderRadius: borderRadius.medium,
    padding: spacing.md,
    marginBottom: spacing.sm,
    borderWidth: 1,
    borderColor: colors.border,
    ...commonStyles.shadowSmall,
  },
  selectedMealImage: {
    width: 80,
    height: 60,
    borderRadius: borderRadius.small,
  },
  selectedMealInfo: {
    flex: 1,
    marginLeft: spacing.md,
  },
  selectedMealName: {
    fontSize: fonts.sizes.medium,
    fontWeight: '600',
    color: colors.text,
    marginBottom: spacing.xs,
  },
  selectedMealDescription: {
    fontSize: fonts.sizes.small,
    color: colors.textSecondary,
    marginBottom: spacing.xs,
    lineHeight: 16,
  },
  selectedNutritionSummary: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: spacing.xs,
  },
  selectedNutritionText: {
    fontSize: fonts.sizes.small,
    color: colors.textSecondary,
    marginRight: spacing.sm,
  },
  selectedMealMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
  },
  selectedCategoryTag: {
    backgroundColor: colors.primary,
    paddingHorizontal: spacing.sm,
    paddingVertical: 2,
    borderRadius: borderRadius.small,
    marginRight: spacing.sm,
  },
  selectedCategoryText: {
    color: colors.surface,
    fontSize: fonts.sizes.tiny || 10,
    fontWeight: '500',
  },
  selectedRatingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  selectedRatingText: {
    fontSize: fonts.sizes.small,
    color: colors.textSecondary,
    marginLeft: 2,
  },
  removeMealButton: {
    padding: spacing.sm,
  },
  emptyMealsContainer: {
    alignItems: 'center',
    paddingVertical: spacing.xl,
  },
  emptyMealsText: {
    fontSize: fonts.sizes.medium,
    fontWeight: '500',
    color: colors.textSecondary,
    marginTop: spacing.md,
  },
  emptyMealsSubtext: {
    fontSize: fonts.sizes.small,
    color: colors.textSecondary,
    marginTop: spacing.xs,
    textAlign: 'center',
  },
  summarySection: {
    backgroundColor: colors.surface,
    padding: spacing.md,
    marginTop: spacing.md,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: spacing.sm,
  },
  summaryMealType: {
    fontSize: fonts.sizes.medium,
    fontWeight: '500',
    color: colors.text,
  },
  summaryCount: {
    fontSize: fonts.sizes.medium,
    color: colors.textSecondary,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: colors.background,
  },
  modalHeader: {
    backgroundColor: colors.surface,
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.md,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  modalTitle: {
    fontSize: fonts.sizes.large,
    fontWeight: 'bold',
    color: colors.text,
  },
  modalCancelText: {
    fontSize: fonts.sizes.medium,
    color: colors.textSecondary,
  },
  modalDoneText: {
    fontSize: fonts.sizes.medium,
    color: colors.primary,
    fontWeight: 'bold',
  },
  searchContainer: {
    backgroundColor: colors.surface,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.md,
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.background,
    borderRadius: borderRadius.medium,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
  },
  searchInput: {
    flex: 1,
    fontSize: fonts.sizes.medium,
    color: colors.text,
    marginLeft: spacing.sm,
  },
  mealsList: {
    padding: spacing.md,
  },
  mealSelectorCard: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: colors.surface,
    borderRadius: borderRadius.medium,
    padding: spacing.md,
    marginBottom: spacing.md,
    borderWidth: 1,
    borderColor: colors.border,
    ...commonStyles.shadowSmall,
  },
  mealSelectorCardSelected: {
    borderColor: colors.primary,
    backgroundColor: colors.background,
    borderWidth: 2,
  },
  mealSelectorImage: {
    width: 100,
    height: 75,
    borderRadius: borderRadius.small,
  },
  mealSelectorInfo: {
    flex: 1,
    marginLeft: spacing.md,
  },
  mealSelectorName: {
    fontSize: fonts.sizes.medium,
    fontWeight: '600',
    color: colors.text,
    marginBottom: spacing.xs,
  },
  mealDescription: {
    fontSize: fonts.sizes.small,
    color: colors.textSecondary,
    marginBottom: spacing.sm,
    lineHeight: 18,
  },
  nutritionInfo: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: spacing.sm,
  },
  nutritionItem: {
    marginRight: spacing.md,
    marginBottom: spacing.xs,
  },
  nutritionLabel: {
    fontSize: fonts.sizes.tiny || 10,
    color: colors.textSecondary,
    textTransform: 'uppercase',
    fontWeight: '500',
  },
  nutritionValue: {
    fontSize: fonts.sizes.small,
    color: colors.text,
    fontWeight: '600',
  },
  mealSelectorMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
    marginBottom: spacing.xs,
  },
  categoryTag: {
    backgroundColor: colors.primary,
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: borderRadius.small,
    marginRight: spacing.sm,
    marginBottom: spacing.xs,
  },
  categoryTagText: {
    color: colors.surface,
    fontSize: fonts.sizes.small,
    fontWeight: '500',
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: spacing.sm,
    marginBottom: spacing.xs,
  },
  ratingText: {
    fontSize: fonts.sizes.small,
    color: colors.textSecondary,
    marginLeft: 2,
  },
  timeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.xs,
  },
  timeText: {
    fontSize: fonts.sizes.small,
    color: colors.textSecondary,
    marginLeft: 2,
  },
  dietaryTags: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  dietaryTag: {
    backgroundColor: colors.background,
    borderWidth: 1,
    borderColor: colors.border,
    paddingHorizontal: spacing.sm,
    paddingVertical: 2,
    borderRadius: borderRadius.small,
    marginRight: spacing.xs,
    marginBottom: spacing.xs,
  },
  dietaryTagText: {
    fontSize: fonts.sizes.tiny || 10,
    color: colors.textSecondary,
    fontWeight: '500',
  },
  mealSelectorAction: {
    padding: spacing.sm,
    alignItems: 'center',
    justifyContent: 'center',
  },
  recommendationsSection: {
    backgroundColor: colors.surface,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  recommendationsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.md,
    marginBottom: spacing.md,
  },
  recommendationsTitle: {
    fontSize: fonts.sizes.medium,
    fontWeight: '600',
    color: colors.text,
    marginLeft: spacing.sm,
    flex: 1,
  },
  recommendationsList: {
    paddingHorizontal: spacing.md,
  },
  recommendationCard: {
    width: 140,
    backgroundColor: colors.background,
    borderRadius: borderRadius.medium,
    marginRight: spacing.md,
    borderWidth: 1,
    borderColor: colors.border,
    overflow: 'hidden',
  },
  recommendationCardSelected: {
    borderColor: colors.primary,
    borderWidth: 2,
  },
  recommendationImage: {
    width: '100%',
    height: 80,
  },
  recommendationInfo: {
    padding: spacing.sm,
    flex: 1,
  },
  recommendationName: {
    fontSize: fonts.sizes.small,
    fontWeight: '600',
    color: colors.text,
    marginBottom: spacing.xs,
    lineHeight: 16,
  },
  recommendationCalories: {
    fontSize: fonts.sizes.tiny || 10,
    color: colors.textSecondary,
    marginBottom: spacing.xs,
  },
  recommendationReason: {
    fontSize: fonts.sizes.tiny || 10,
    color: colors.primary,
    fontStyle: 'italic',
  },
  recommendationAction: {
    position: 'absolute',
    top: spacing.sm,
    right: spacing.sm,
    backgroundColor: colors.surface,
    borderRadius: 12,
    padding: 2,
  },
  recommendationImageContainer: {
    position: 'relative',
  },
  detailsOverlay: {
    position: 'absolute',
    bottom: 4,
    left: 4,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    borderRadius: 12,
    paddingHorizontal: 6,
    paddingVertical: 2,
    flexDirection: 'row',
    alignItems: 'center',
  },
  detailsText: {
    color: colors.surface,
    fontSize: 10,
    marginLeft: 2,
    fontWeight: '500',
  },
  mealImageContainer: {
    position: 'relative',
  },
  dietaryBadges: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: spacing.xs,
  },
  dietaryBadge: {
    paddingHorizontal: spacing.sm,
    paddingVertical: 2,
    borderRadius: borderRadius.small,
    marginRight: spacing.xs,
    marginBottom: spacing.xs,
  },
  dietaryBadgeText: {
    fontSize: fonts.sizes.tiny || 10,
    fontWeight: '500',
    color: colors.surface,
  },
  vegetarianBadge: {
    backgroundColor: '#4CAF50',
  },
  veganBadge: {
    backgroundColor: '#2E7D32',
  },
  glutenFreeBadge: {
    backgroundColor: '#FF9800',
  },
  dairyFreeBadge: {
    backgroundColor: '#2196F3',
  },
  nutFreeBadge: {
    backgroundColor: '#9C27B0',
  },
  lowCarbBadge: {
    backgroundColor: '#F44336',
  },
  ketoBadge: {
    backgroundColor: '#E91E63',
  },
  pescatarianBadge: {
    backgroundColor: '#00BCD4',
  },
  halalBadge: {
    backgroundColor: '#4CAF50',
  },
  riceBowlsSection: {
    backgroundColor: colors.surface,
    margin: spacing.md,
    padding: spacing.md,
    borderRadius: borderRadius.medium,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  riceBowlsDescription: {
    fontSize: fonts.sizes.small,
    color: colors.textSecondary,
    marginBottom: spacing.md,
  },
  riceBowlsInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  riceBowlsButton: {
    backgroundColor: colors.surface,
    borderWidth: 1,
    borderColor: colors.primary,
    borderRadius: borderRadius.medium,
    padding: spacing.sm,
    marginHorizontal: spacing.sm,
  },
  riceBowlsInputWrapper: {
    backgroundColor: colors.background,
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: borderRadius.medium,
    paddingHorizontal: spacing.md,
    minWidth: 80,
  },
  riceBowlsInput: {
    fontSize: fonts.sizes.medium,
    textAlign: 'center',
    paddingVertical: spacing.sm,
    color: colors.text,
  },

  // AI Compatibility Styles for Mobile
  mealCompatibilityPending: {
    backgroundColor: '#fff3cd',
    borderRadius: borderRadius.small,
    padding: spacing.sm,
    marginTop: spacing.sm,
    borderLeftWidth: 2,
    borderLeftColor: '#ffc107',
  },
  pendingText: {
    fontSize: fonts.sizes.small,
    color: '#856404',
    fontWeight: '500',
  },
  mealCompatibility: {
    backgroundColor: colors.background,
    borderRadius: borderRadius.small,
    padding: spacing.sm,
    marginTop: spacing.sm,
    borderWidth: 1,
    borderColor: colors.border,
  },
  compatibilityHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.xs,
  },
  compatibilityTitle: {
    fontSize: fonts.sizes.small,
    fontWeight: '600',
    color: colors.text,
  },
  compatibilityBadge: {
    paddingHorizontal: spacing.sm,
    paddingVertical: 2,
    borderRadius: borderRadius.small,
  },
  compatibilityBadgeText: {
    fontSize: fonts.sizes.tiny || 10,
    fontWeight: '500',
    color: colors.surface,
  },
  compatibilityExcellent: {
    backgroundColor: '#4CAF50',
  },
  compatibilityGood: {
    backgroundColor: '#FFC107',
  },
  compatibilityFair: {
    backgroundColor: '#FF9800',
  },
  compatibilityPoor: {
    backgroundColor: '#F44336',
  },
  compatibilityPending: {
    backgroundColor: '#9E9E9E',
  },
  compatibilityUnknown: {
    backgroundColor: '#9E9E9E',
  },
  familyCompatibilityList: {
    gap: spacing.xs,
  },
  memberCompatibility: {
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
  },
  memberName: {
    fontSize: fonts.sizes.small,
    fontWeight: '500',
    color: colors.text,
    marginRight: spacing.xs,
  },
  memberConcern: {
    fontSize: fonts.sizes.small,
    flex: 1,
  },
  memberConcernGood: {
    color: '#4CAF50',
  },
  memberConcernBad: {
    color: '#F44336',
  },
  // Compatibility badge styles
  compatibilityExcellent: {
    backgroundColor: '#4CAF50',
  },
  compatibilityGood: {
    backgroundColor: '#8BC34A',
  },
  compatibilityFair: {
    backgroundColor: '#FF9800',
  },
  compatibilityPoor: {
    backgroundColor: '#F44336',
  },
  compatibilityPending: {
    backgroundColor: '#9E9E9E',
  },
  compatibilityUnknown: {
    backgroundColor: '#9E9E9E',
  },

  // Validation Modal Styles
  validationModalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  validationModalContent: {
    backgroundColor: colors.surface,
    borderRadius: borderRadius.medium,
    margin: spacing.md,
    maxHeight: '80%',
    width: '90%',
  },
  validationModalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  validationModalTitle: {
    fontSize: fonts.sizes.large,
    fontWeight: 'bold',
    color: colors.text,
  },
  validationModalCloseButton: {
    padding: spacing.xs,
  },
  validationLoadingContainer: {
    padding: spacing.lg,
    alignItems: 'center',
  },
  validationLoadingText: {
    fontSize: fonts.sizes.medium,
    color: colors.text,
    textAlign: 'center',
  },
  validationResultsContainer: {
    maxHeight: 400,
  },
  validationSummaryText: {
    fontSize: fonts.sizes.medium,
    fontWeight: '600',
    color: colors.text,
    textAlign: 'center',
    padding: spacing.md,
    backgroundColor: colors.background,
    margin: spacing.md,
    borderRadius: borderRadius.small,
  },
  validationMembersContainer: {
    padding: spacing.md,
  },
  validationMemberCard: {
    backgroundColor: colors.background,
    borderRadius: borderRadius.small,
    padding: spacing.md,
    marginBottom: spacing.sm,
    borderWidth: 1,
    borderColor: colors.border,
  },
  validationMemberHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.xs,
  },
  validationMemberName: {
    fontSize: fonts.sizes.medium,
    fontWeight: '600',
    color: colors.text,
  },
  validationStatusBadge: {
    paddingHorizontal: spacing.sm,
    paddingVertical: 4,
    borderRadius: borderRadius.small,
  },
  validationStatusGood: {
    backgroundColor: '#4CAF50',
  },
  validationStatusWarning: {
    backgroundColor: '#FF9800',
  },
  validationStatusText: {
    fontSize: fonts.sizes.small,
    fontWeight: '500',
    color: colors.surface,
  },
  validationConcernText: {
    fontSize: fonts.sizes.small,
    lineHeight: 18,
  },
  validationConcernGood: {
    color: '#4CAF50',
  },
  validationConcernWarning: {
    color: '#FF9800',
  },
  validationModalActions: {
    flexDirection: 'row',
    padding: spacing.md,
    borderTopWidth: 1,
    borderTopColor: colors.border,
    gap: spacing.sm,
  },
  validationActionButton: {
    flex: 1,
    paddingVertical: spacing.md,
    borderRadius: borderRadius.small,
    alignItems: 'center',
  },
  validationPrimaryButton: {
    backgroundColor: colors.primary,
  },
  validationSecondaryButton: {
    backgroundColor: colors.background,
    borderWidth: 1,
    borderColor: colors.border,
  },
  validationPrimaryButtonText: {
    fontSize: fonts.sizes.medium,
    fontWeight: '600',
    color: colors.surface,
  },
  validationSecondaryButtonText: {
    fontSize: fonts.sizes.medium,
    fontWeight: '600',
    color: colors.text,
  },
});

export default CreateMealPlanScreen;
