import React from 'react';
import {
  View,
  Text,
  Modal,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
} from 'react-native';
import { colors, legacyFonts, legacySpacing, borderRadius } from '../styles/theme';

const TermsModal = ({ visible, onAccept, onDecline }) => {
  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onDecline}
    >
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.headerTitle}>Terms and Conditions & Data Privacy</Text>
          <Text style={styles.headerSubtitle}>Please read and accept our terms to continue</Text>
        </View>
        
        <ScrollView style={styles.content} showsVerticalScrollIndicator={true}>
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Terms and Conditions</Text>
            <Text style={styles.paragraph}>
              By using PanlasApp, you agree to the following terms and conditions:
            </Text>
            <View style={styles.bulletList}>
              <Text style={styles.bulletItem}>• You will use the app responsibly and in accordance with applicable laws</Text>
              <Text style={styles.bulletItem}>• You are responsible for maintaining the confidentiality of your account</Text>
              <Text style={styles.bulletItem}>• You will provide accurate and up-to-date information</Text>
              <Text style={styles.bulletItem}>• You understand that meal recommendations are for informational purposes only</Text>
              <Text style={styles.bulletItem}>• You will not misuse the app or attempt to harm its functionality</Text>
            </View>
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Data Privacy Policy</Text>
            <Text style={styles.paragraph}>
              We are committed to protecting your privacy and personal information:
            </Text>
            <View style={styles.bulletList}>
              <Text style={styles.bulletItem}>
                <Text style={styles.bold}>Data Collection:</Text> We collect only necessary information to provide our services, including your name, email, dietary preferences, and family information
              </Text>
              <Text style={styles.bulletItem}>
                <Text style={styles.bold}>Data Usage:</Text> Your data is used to personalize meal recommendations, manage your family profiles, and improve our services
              </Text>
              <Text style={styles.bulletItem}>
                <Text style={styles.bold}>Data Sharing:</Text> We do not sell or share your personal information with third parties without your consent
              </Text>
              <Text style={styles.bulletItem}>
                <Text style={styles.bold}>Data Security:</Text> We implement appropriate security measures to protect your information
              </Text>
              <Text style={styles.bulletItem}>
                <Text style={styles.bold}>Data Retention:</Text> We retain your data only as long as necessary to provide our services
              </Text>
              <Text style={styles.bulletItem}>
                <Text style={styles.bold}>Your Rights:</Text> You have the right to access, update, or delete your personal information at any time
              </Text>
            </View>
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Contact Information</Text>
            <Text style={styles.paragraph}>
              If you have any questions about these terms or our privacy practices, please contact us at:
            </Text>
            <Text style={styles.contactInfo}>
              <Text style={styles.bold}>Email:</Text> <EMAIL>{'\n'}
              <Text style={styles.bold}>Phone:</Text> Tel: 8-4248370
            </Text>
            <Text style={styles.lastUpdated}>
              <Text style={styles.bold}>Last Updated:</Text> {new Date().toLocaleDateString()}
            </Text>
          </View>
        </ScrollView>
        
        <View style={styles.actions}>
          <TouchableOpacity 
            style={[styles.button, styles.declineButton]} 
            onPress={onDecline}
          >
            <Text style={styles.declineButtonText}>Decline</Text>
          </TouchableOpacity>
          <TouchableOpacity 
            style={[styles.button, styles.acceptButton]} 
            onPress={onAccept}
          >
            <Text style={styles.acceptButtonText}>Accept & Continue</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    backgroundColor: colors.primary,
    paddingVertical: legacySpacing.lg,
    paddingHorizontal: legacySpacing.lg,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: legacyFonts.sizes.large,
    fontWeight: 'bold',
    color: colors.surface,
    textAlign: 'center',
    marginBottom: legacySpacing.sm,
  },
  headerSubtitle: {
    fontSize: legacyFonts.sizes.medium,
    color: colors.surface,
    textAlign: 'center',
    opacity: 0.9,
  },
  content: {
    flex: 1,
    paddingHorizontal: legacySpacing.lg,
  },
  section: {
    marginVertical: legacySpacing.lg,
  },
  sectionTitle: {
    fontSize: legacyFonts.sizes.large,
    fontWeight: 'bold',
    color: colors.text,
    marginBottom: legacySpacing.md,
  },
  paragraph: {
    fontSize: legacyFonts.sizes.medium,
    color: colors.textSecondary,
    lineHeight: 24,
    marginBottom: legacySpacing.md,
  },
  bulletList: {
    marginLeft: legacySpacing.sm,
  },
  bulletItem: {
    fontSize: legacyFonts.sizes.medium,
    color: colors.textSecondary,
    lineHeight: 24,
    marginBottom: legacySpacing.sm,
  },
  bold: {
    fontWeight: 'bold',
    color: colors.text,
  },
  contactInfo: {
    fontSize: legacyFonts.sizes.medium,
    color: colors.textSecondary,
    lineHeight: 24,
    marginBottom: legacySpacing.md,
  },
  lastUpdated: {
    fontSize: legacyFonts.sizes.medium,
    color: colors.textSecondary,
    fontStyle: 'italic',
  },
  actions: {
    flexDirection: 'row',
    paddingHorizontal: legacySpacing.lg,
    paddingVertical: legacySpacing.lg,
    gap: legacySpacing.md,
    borderTopWidth: 1,
    borderTopColor: colors.border,
  },
  button: {
    flex: 1,
    paddingVertical: legacySpacing.md,
    borderRadius: borderRadius.medium,
    alignItems: 'center',
  },
  declineButton: {
    backgroundColor: colors.surface,
    borderWidth: 1,
    borderColor: colors.border,
  },
  acceptButton: {
    backgroundColor: colors.primary,
  },
  declineButtonText: {
    fontSize: legacyFonts.sizes.medium,
    fontWeight: 'bold',
    color: colors.textSecondary,
  },
  acceptButtonText: {
    fontSize: legacyFonts.sizes.medium,
    fontWeight: 'bold',
    color: colors.surface,
  },
});

export default TermsModal;
