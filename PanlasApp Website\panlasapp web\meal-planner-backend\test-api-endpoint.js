require('dotenv').config();
const express = require('express');
const mongoose = require('mongoose');
const cors = require('cors');
const mealPlanController = require('./controllers/mealPlanController');
const auth = require('./middleware/auth');

// Create a simple test server
const app = express();

app.use(cors());
app.use(express.json());

// Mock auth middleware for testing
const mockAuth = (req, res, next) => {
  req.user = { id: '685883ea3cc2df1d683b8714' }; // User ID from debug
  next();
};

// Test route
app.put('/meal-plans/:date/lock', mockAuth, mealPlanController.toggleLockMealPlan);

async function testAPIEndpoint() {
  try {
    console.log('🔍 Connecting to MongoDB...');
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ Connected to MongoDB');

    const PORT = 5001; // Use different port to avoid conflicts
    const server = app.listen(PORT, () => {
      console.log(`🚀 Test server running on port ${PORT}`);
    });

    // Wait a moment for server to start
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Test the API endpoint
    console.log('\n🧪 Testing API endpoint...');
    
    const axios = require('axios');
    
    try {
      const response = await axios.put(`http://localhost:${PORT}/meal-plans/2025-08-14/lock`, {
        isLocked: true
      });
      
      console.log('✅ API Response:');
      console.log('Status:', response.status);
      console.log('Data:', response.data);
      
    } catch (error) {
      if (error.response) {
        console.log('❌ API Error Response:');
        console.log('Status:', error.response.status);
        console.log('Data:', error.response.data);
      } else {
        console.log('❌ Network Error:', error.message);
      }
    }

    // Close server
    server.close();
    console.log('\n🔌 Test server closed');

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from MongoDB');
  }
}

testAPIEndpoint();
