const axios = require('axios');
const mongoose = require('mongoose');
const User = require('../models/User');
require('dotenv').config();

const baseURL = 'http://localhost:5000/api/ai';

async function testMealPlanGeneration() {
  try {
    console.log('🧪 Testing Meal Plan Generation...\n');

    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ Connected to MongoDB');

    // Find or create a test user
    let testUser = await User.findOne({ email: '<EMAIL>' });
    if (!testUser) {
      testUser = new User({
        name: 'Test User',
        email: '<EMAIL>',
        password: 'hashedpassword',
        dietaryPreferences: {
          restrictions: ['Halal', 'Dairy-Free'],
          allergies: ['Nuts'],
          dislikedIngredients: ['Mushrooms'],
          calorieTarget: 2000,
          mealFrequency: 3
        },
        familyMembers: [
          {
            name: 'Family Member 1',
            relationship: 'Spouse',
            dateOfBirth: new Date('1990-01-01'),
            dietaryPreferences: {
              restrictions: ['Vegetarian'],
              allergies: ['Shellfish'],
              dislikedIngredients: [],
              calorieTarget: 1800,
              mealFrequency: 3
            }
          }
        ]
      });
      await testUser.save();
      console.log('✅ Created test user');
    } else {
      console.log('✅ Found existing test user');
    }

    // Generate JWT token for testing
    const jwt = require('jsonwebtoken');
    const token = jwt.sign(
      { id: testUser._id, email: testUser.email },
      process.env.JWT_SECRET || 'fallback-secret',
      { expiresIn: '1h' }
    );

    const headers = {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    };

    // Test 1: Generate individual meal plan
    console.log('\n1. Testing POST /generate-meal-plan...');
    try {
      const mealPlanResponse = await axios.post(`${baseURL}/generate-meal-plan`, {}, { headers });
      
      console.log('✅ Individual meal plan generation successful');
      console.log('Response structure:', {
        success: mealPlanResponse.data.success,
        hasMealPlan: !!mealPlanResponse.data.mealPlan,
        hasPersonalizedMessage: !!mealPlanResponse.data.personalizedMessage,
        hasNutritionalSummary: !!mealPlanResponse.data.nutritionalSummary
      });
      
      if (mealPlanResponse.data.mealPlan) {
        console.log('Meal plan structure:');
        console.log('- Breakfast meals:', mealPlanResponse.data.mealPlan.breakfast?.length || 0);
        console.log('- Lunch meals:', mealPlanResponse.data.mealPlan.lunch?.length || 0);
        console.log('- Dinner meals:', mealPlanResponse.data.mealPlan.dinner?.length || 0);
        
        // Show sample meal
        if (mealPlanResponse.data.mealPlan.breakfast && mealPlanResponse.data.mealPlan.breakfast.length > 0) {
          const sampleMeal = mealPlanResponse.data.mealPlan.breakfast[0];
          console.log('Sample breakfast meal:', {
            mealName: sampleMeal.mealName,
            reason: sampleMeal.reason ? sampleMeal.reason.substring(0, 50) + '...' : 'No reason'
          });
        }
      }
    } catch (error) {
      console.error('❌ Individual meal plan generation failed:', error.response?.data || error.message);
    }

    // Test 2: Generate family meal plan
    console.log('\n2. Testing POST /generate-family-meal-plan...');
    try {
      const familyMealPlanResponse = await axios.post(`${baseURL}/generate-family-meal-plan`, {}, { headers });
      
      console.log('✅ Family meal plan generation successful');
      console.log('Response structure:', {
        success: familyMealPlanResponse.data.success,
        hasMealPlan: !!familyMealPlanResponse.data.mealPlan,
        hasPersonalizedMessage: !!familyMealPlanResponse.data.personalizedMessage,
        hasConflicts: !!familyMealPlanResponse.data.conflicts,
        conflictCount: familyMealPlanResponse.data.conflicts?.length || 0
      });
      
      if (familyMealPlanResponse.data.mealPlan) {
        console.log('Family meal plan structure:');
        console.log('- Breakfast meals:', familyMealPlanResponse.data.mealPlan.breakfast?.length || 0);
        console.log('- Lunch meals:', familyMealPlanResponse.data.mealPlan.lunch?.length || 0);
        console.log('- Dinner meals:', familyMealPlanResponse.data.mealPlan.dinner?.length || 0);
      }
    } catch (error) {
      console.error('❌ Family meal plan generation failed:', error.response?.data || error.message);
    }

    // Test 3: Edit meal plan
    console.log('\n3. Testing POST /edit-meal-plan...');
    try {
      const sampleMealPlan = {
        breakfast: [
          { mealName: 'Sinangag', reason: 'A classic Filipino fried rice perfect for breakfast' },
          { mealName: 'Longganisa', reason: 'Traditional Filipino sausage that pairs well with rice' }
        ],
        lunch: [
          { mealName: 'Adobong Manok', reason: 'A popular Filipino chicken dish' }
        ],
        dinner: [
          { mealName: 'Sinigang na Baboy', reason: 'A comforting sour soup perfect for dinner' }
        ]
      };

      const editResponse = await axios.post(`${baseURL}/edit-meal-plan`, {
        currentMealPlan: sampleMealPlan,
        editRequest: 'Replace Sinangag with Chicken Curry in breakfast',
        isFamily: false
      }, { headers });
      
      console.log('✅ Meal plan editing successful');
      console.log('Edit response structure:', {
        success: editResponse.data.success,
        hasMealPlan: !!editResponse.data.mealPlan,
        hasPersonalizedMessage: !!editResponse.data.personalizedMessage
      });
      
      if (editResponse.data.mealPlan && editResponse.data.mealPlan.breakfast) {
        console.log('Updated breakfast meals:');
        editResponse.data.mealPlan.breakfast.forEach((meal, index) => {
          console.log(`  ${index + 1}. ${meal.mealName} - ${meal.reason?.substring(0, 50)}...`);
        });
      }
    } catch (error) {
      console.error('❌ Meal plan editing failed:', error.response?.data || error.message);
    }

    await mongoose.disconnect();
    console.log('\n🔌 Disconnected from MongoDB');
    console.log('\n🎉 Meal plan generation testing completed!');

  } catch (error) {
    console.error('❌ Test setup failed:', error);
  }
}

testMealPlanGeneration();
