const axios = require('axios');

const API_BASE_URL = 'http://localhost:5000/api';

async function testServerConnection() {
  try {
    console.log('🧪 Testing server connection...');
    
    // Test 1: Basic server health check
    console.log('Step 1: Testing basic server connection...');
    try {
      const healthResponse = await axios.get(`${API_BASE_URL}/health`);
      console.log('✅ Server is responding:', healthResponse.status);
    } catch (error) {
      console.log('⚠️ Health endpoint not available, trying meals endpoint...');
      
      // Test 2: Try meals endpoint (usually public)
      try {
        const mealsResponse = await axios.get(`${API_BASE_URL}/meals`);
        console.log('✅ Meals endpoint responding:', mealsResponse.status);
      } catch (mealError) {
        console.log('❌ Server might not be running:', mealError.message);
        return;
      }
    }

    // Test 3: Test meal-plans endpoint without auth (should return 401)
    console.log('Step 2: Testing meal-plans endpoint without auth...');
    try {
      const mealPlanResponse = await axios.post(`${API_BASE_URL}/meal-plans/save`, {
        test: 'data'
      });
      console.log('⚠️ Unexpected success without auth:', mealPlanResponse.status);
    } catch (error) {
      if (error.response && error.response.status === 401) {
        console.log('✅ Endpoint correctly requires authentication (401)');
      } else {
        console.log('❌ Unexpected error:', error.response?.status, error.response?.data);
      }
    }

    // Test 4: Test with invalid token
    console.log('Step 3: Testing with invalid token...');
    try {
      const invalidTokenResponse = await axios.post(`${API_BASE_URL}/meal-plans/save`, {
        name: "Test",
        startDate: "2025-01-31",
        endDate: "2025-01-31",
        meals: []
      }, {
        headers: {
          'Authorization': 'Bearer invalid-token',
          'Content-Type': 'application/json'
        }
      });
      console.log('⚠️ Unexpected success with invalid token:', invalidTokenResponse.status);
    } catch (error) {
      if (error.response && error.response.status === 401) {
        console.log('✅ Endpoint correctly rejects invalid token (401)');
      } else {
        console.log('❌ Unexpected error with invalid token:', error.response?.status, error.response?.data);
      }
    }

    console.log('\n📋 Summary:');
    console.log('- Server is running and responding');
    console.log('- Authentication is working (rejects unauthorized requests)');
    console.log('- The issue is likely with the token or data validation');
    console.log('\n💡 Next steps:');
    console.log('1. Check if you are logged in properly');
    console.log('2. Check browser console for detailed error messages');
    console.log('3. Verify the meal data structure matches backend expectations');

  } catch (error) {
    console.error('❌ Server connection test failed:', error.message);
  }
}

// Run the test
testServerConnection();
