import React, { useState, useEffect } from 'react';
import axios from 'axios';
import './SystemHealth.css';

function SystemHealth() {
  const [healthData, setHealthData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [refreshInterval, setRefreshInterval] = useState(30); // seconds
  const [lastRefreshed, setLastRefreshed] = useState(null);

  const fetchHealthData = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('token');
      
      const config = {
        headers: {
          'x-auth-token': token
        }
      };

      const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';
      const response = await axios.get(`${API_BASE_URL}/admin/system/health`, config);
      setHealthData(response.data);
      setLastRefreshed(new Date());
      setLoading(false);
    } catch (err) {
      setError('Failed to fetch system health data');
      console.error('System health error:', err);
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchHealthData();
    
    // Set up interval for refreshing data
    const intervalId = setInterval(() => {
      fetchHealthData();
    }, refreshInterval * 1000);
    
    // Clean up interval on component unmount
    return () => clearInterval(intervalId);
  }, [refreshInterval]);

  const handleRefreshChange = (e) => {
    const value = parseInt(e.target.value);
    setRefreshInterval(value);
  };

  const formatUptime = (seconds) => {
    const days = Math.floor(seconds / (3600 * 24));
    const hours = Math.floor((seconds % (3600 * 24)) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);
    
    return `${days}d ${hours}h ${minutes}m ${secs}s`;
  };

  const formatBytes = (bytes, decimals = 2) => {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const dm = decimals < 0 ? 0 : decimals;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
  };

  const getStatusClass = (value, thresholds) => {
    if (value >= thresholds.danger) return 'status-danger';
    if (value >= thresholds.warning) return 'status-warning';
    return 'status-good';
  };

  if (loading && !healthData) return <div className="loading-health">Loading system health data...</div>;
  if (error && !healthData) return <div className="error-health">{error}</div>;

  return (
    <div className="system-health">
      <div className="health-header">
        <h2>System Health Monitor</h2>
        <div className="refresh-controls">
          <span>Auto-refresh: </span>
          <select value={refreshInterval} onChange={handleRefreshChange}>
            <option value="10">10 seconds</option>
            <option value="30">30 seconds</option>
            <option value="60">1 minute</option>
            <option value="300">5 minutes</option>
          </select>
          <button className="refresh-button" onClick={fetchHealthData} disabled={loading}>
            {loading ? 'Refreshing...' : 'Refresh Now'}
          </button>
        </div>
      </div>
      
      {lastRefreshed && (
        <div className="last-refreshed">
          Last updated: {lastRefreshed.toLocaleTimeString()}
        </div>
      )}
      
      {healthData && (
        <div className="health-grid">
          {/* Server Uptime */}
          <div className="health-card">
            <h3>Server Uptime</h3>
            <div className="health-value">{formatUptime(healthData.uptime)}</div>
            <div className="health-detail">Started: {new Date(healthData.startTime).toLocaleString()}</div>
          </div>
          
          {/* Memory Usage */}
          <div className="health-card">
            <h3>Memory Usage</h3>
            <div className={`health-value ${getStatusClass(healthData.memory.usedPercentage, { warning: 70, danger: 85 })}`}>
              {healthData.memory.usedPercentage.toFixed(1)}%
            </div>
            <div className="health-detail">
              {formatBytes(healthData.memory.used)} / {formatBytes(healthData.memory.total)}
            </div>
            <div className="progress-bar">
              <div 
                className={`progress ${getStatusClass(healthData.memory.usedPercentage, { warning: 70, danger: 85 })}`} 
                style={{ width: `${healthData.memory.usedPercentage}%` }}
              ></div>
            </div>
          </div>
          
          {/* CPU Usage */}
          <div className="health-card">
            <h3>CPU Usage</h3>
            <div className={`health-value ${getStatusClass(healthData.cpu.usage, { warning: 60, danger: 80 })}`}>
              {healthData.cpu.usage.toFixed(1)}%
            </div>
            <div className="health-detail">
              Cores: {healthData.cpu.cores}
            </div>
            <div className="progress-bar">
              <div 
                className={`progress ${getStatusClass(healthData.cpu.usage, { warning: 60, danger: 80 })}`} 
                style={{ width: `${healthData.cpu.usage}%` }}
              ></div>
            </div>
          </div>
          
          {/* Database Status */}
          <div className="health-card">
            <h3>Database Status</h3>
            <div className={`health-value ${healthData.database.connected ? 'status-good' : 'status-danger'}`}>
              {healthData.database.connected ? 'Connected' : 'Disconnected'}
            </div>
            <div className="health-detail">
              Response Time: {healthData.database.responseTime}ms
            </div>
          </div>
          
          {/* API Health */}
          <div className="health-card">
            <h3>API Health</h3>
            <div className={`health-value ${healthData.api.status === 'healthy' ? 'status-good' : 'status-danger'}`}>
              {healthData.api.status}
            </div>
            <div className="health-detail">
              Response Time: {healthData.api.responseTime}ms
            </div>
          </div>
          
          {/* Active Users */}
          <div className="health-card">
            <h3>Active Users</h3>
            <div className="health-value">{healthData.activeUsers.count}</div>
            <div className="health-detail">
              Last 24 hours: {healthData.activeUsers.last24Hours}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default SystemHealth;
