const mongoose = require('mongoose');
const User = require('./models/User');

// Connect to MongoDB
mongoose.connect('mongodb://localhost:27017/meal-planner', {
  useNewUrlParser: true,
  useUnifiedTopology: true,
});

async function testDislikedIngredientsUpdate() {
  try {
    console.log('🔍 Testing disliked ingredients update functionality...');
    
    // Find a user with family members
    const user = await User.findOne({ 'familyMembers.0': { $exists: true } });
    
    if (!user) {
      console.log('❌ No user with family members found');
      return;
    }
    
    console.log('✅ Found user:', user.email);
    console.log('👨‍👩‍👧‍👦 Family members count:', user.familyMembers.length);
    
    // Find the first family member
    const familyMember = user.familyMembers[0];
    console.log('👤 Testing with family member:', familyMember.name);
    console.log('🔍 Current disliked ingredients:', familyMember.dietaryPreferences?.dislikedIngredients);
    
    // Test update - change disliked ingredients
    const originalIngredients = familyMember.dietaryPreferences?.dislikedIngredients || [];
    const newIngredients = ['daal', 'onions', 'garlic']; // Test ingredients
    
    console.log('🔄 Updating disliked ingredients from:', originalIngredients);
    console.log('🔄 Updating disliked ingredients to:', newIngredients);
    
    // Find the family member and update
    const currentMember = user.familyMembers.id(familyMember._id);
    
    if (!currentMember) {
      console.log('❌ Family member not found');
      return;
    }
    
    // Update the disliked ingredients
    currentMember.dietaryPreferences = {
      ...currentMember.dietaryPreferences,
      dislikedIngredients: newIngredients
    };
    
    // Save the user
    await user.save();
    
    console.log('✅ Update completed, verifying...');
    
    // Fetch the user again to verify the update
    const updatedUser = await User.findById(user._id);
    const updatedMember = updatedUser.familyMembers.id(familyMember._id);
    
    console.log('🔍 Updated disliked ingredients:', updatedMember.dietaryPreferences?.dislikedIngredients);
    
    // Verify the update worked
    const updatedIngredients = updatedMember.dietaryPreferences?.dislikedIngredients || [];
    const isUpdateSuccessful = JSON.stringify(updatedIngredients.sort()) === JSON.stringify(newIngredients.sort());
    
    if (isUpdateSuccessful) {
      console.log('✅ Update successful! Disliked ingredients were updated correctly.');
    } else {
      console.log('❌ Update failed! Disliked ingredients were not updated correctly.');
      console.log('Expected:', newIngredients);
      console.log('Actual:', updatedIngredients);
    }
    
    // Restore original ingredients
    console.log('🔄 Restoring original ingredients...');
    updatedMember.dietaryPreferences.dislikedIngredients = originalIngredients;
    await updatedUser.save();
    console.log('✅ Original ingredients restored');
    
  } catch (error) {
    console.error('❌ Test failed with error:', error);
  } finally {
    mongoose.connection.close();
  }
}

testDislikedIngredientsUpdate();
