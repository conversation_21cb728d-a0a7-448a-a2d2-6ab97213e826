const geminiService = require('./services/geminiService');

// Test data
const testUserProfile = {
  dietaryPreferences: {
    restrictions: ['Vegetarian'],
    allergies: ['Nuts'],
    dislikedIngredients: [],
    calorieTarget: 2000,
    mealFrequency: 3
  }
};

const testFamilyMembers = [
  {
    name: 'Maria',
    dietaryPreferences: {
      restrictions: ['Halal'],
      allergies: ['Milk'],
      dislikedIngredients: [],
      calorieTarget: 1800,
      mealFrequency: 3
    }
  },
  {
    name: '<PERSON>',
    dietaryPreferences: {
      restrictions: [],
      allergies: [],
      dislikedIngredients: [],
      calorieTarget: 2200,
      mealFrequency: 3
    }
  }
];

const testSelectedMeals = [
  {
    name: 'Chicken Adobo',
    calories: 450,
    ingredients: ['chicken', 'soy sauce', 'vinegar', 'garlic'],
    allergens: [],
    dietType: 'general'
  },
  {
    name: 'Beef Steak',
    calories: 600,
    ingredients: ['beef', 'onions', 'soy sauce'],
    allergens: [],
    dietType: 'general'
  },
  {
    name: 'Pork Sisig',
    calories: 520,
    ingredients: ['pork', 'onions', 'chili'],
    allergens: [],
    dietType: 'general'
  },
  {
    name: 'Nut Cookies',
    calories: 300,
    ingredients: ['flour', 'nuts', 'sugar'],
    allergens: ['Nuts'],
    dietType: 'dessert'
  },
  {
    name: 'Milk Tea',
    calories: 200,
    ingredients: ['tea', 'milk', 'sugar'],
    allergens: ['Milk'],
    dietType: 'beverage'
  }
];

async function testValidation() {
  console.log('🧪 Testing Meal Plan Validation...\n');
  
  try {
    console.log('📋 Test Data:');
    console.log('User Profile:', JSON.stringify(testUserProfile, null, 2));
    console.log('Family Members:', JSON.stringify(testFamilyMembers, null, 2));
    console.log('Selected Meals:', testSelectedMeals.map(m => m.name).join(', '));
    console.log('\n🔍 Running validation...\n');

    const result = await geminiService.validateMealPlan(
      testUserProfile,
      testFamilyMembers,
      testSelectedMeals
    );

    console.log('✅ Validation Result:');
    console.log(JSON.stringify(result, null, 2));

    // Analyze results
    console.log('\n📊 Analysis:');
    console.log(`Total Calories: ${result.totalCalories}`);
    console.log(`Has Issues: ${result.hasIssues}`);
    console.log(`Members Analyzed: ${result.memberValidations.length}`);
    
    result.memberValidations.forEach(member => {
      const status = member.concern === 'All is well' ? '✅' : '⚠️';
      console.log(`${status} ${member.memberName}: ${member.concern}`);
    });

    // Test fallback functionality
    console.log('\n🔧 Testing fallback validation...');
    const fallbackResult = geminiService.createMealPlanValidationFallback(
      [
        { name: 'You', dietaryPreferences: testUserProfile.dietaryPreferences, calorieTarget: 2000 },
        ...testFamilyMembers.map(m => ({ name: m.name, dietaryPreferences: m.dietaryPreferences, calorieTarget: m.dietaryPreferences.calorieTarget }))
      ],
      testSelectedMeals,
      testSelectedMeals.reduce((sum, meal) => sum + meal.calories, 0)
    );

    console.log('🔧 Fallback Result:');
    fallbackResult.forEach(member => {
      const status = member.concern === 'All is well' ? '✅' : '⚠️';
      console.log(`${status} ${member.memberName}: ${member.concern}`);
    });

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack trace:', error.stack);
  }
}

// Run the test
if (require.main === module) {
  testValidation().then(() => {
    console.log('\n🎉 Test completed!');
    process.exit(0);
  }).catch(error => {
    console.error('💥 Test crashed:', error);
    process.exit(1);
  });
}

module.exports = { testValidation };
