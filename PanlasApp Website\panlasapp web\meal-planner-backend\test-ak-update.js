const mongoose = require('mongoose');
const User = require('./models/User');

// Connect to MongoDB - Use the same connection as the server
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb+srv://johnmatthewmartin09:<EMAIL>/mealplanner?retryWrites=true&w=majority&appName=MealPlannersCluster';
mongoose.connect(MONGODB_URI);

async function testAkUpdate() {
  try {
    console.log('🔍 Testing update of Ak\'s disliked ingredients...');
    
    // Find user with family member named "Ak"
    const user = await User.findOne({ 
      'familyMembers.name': { $regex: /^Ak$/i }
    });
    
    if (!user) {
      console.log('❌ No user found with family member named "Ak"');
      return;
    }
    
    console.log('✅ Found user:', user.email);
    
    // Find the "Ak" family member
    const akMember = user.familyMembers.find(member => 
      member.name.toLowerCase() === 'ak'
    );
    
    if (!akMember) {
      console.log('❌ Ak family member not found');
      return;
    }
    
    console.log('👤 Current Ak data:');
    console.log('  - Disliked Ingredients:', akMember.dietaryPreferences?.dislikedIngredients);
    
    // Save original ingredients for restoration
    const originalIngredients = [...(akMember.dietaryPreferences?.dislikedIngredients || [])];
    console.log('💾 Original ingredients saved:', originalIngredients);
    
    // Test 1: Update to new ingredients (simulating user changing from Elaichi to daal)
    console.log('\n🔄 Test 1: Updating disliked ingredients from Elaichi to daal...');
    
    const newIngredients = ['daal'];
    akMember.dietaryPreferences.dislikedIngredients = newIngredients;
    
    await user.save();
    console.log('✅ Update saved to database');
    
    // Verify the update by fetching fresh data
    const updatedUser = await User.findById(user._id);
    const updatedAkMember = updatedUser.familyMembers.find(member => 
      member.name.toLowerCase() === 'ak'
    );
    
    console.log('🔍 Verification - Fresh data from database:');
    console.log('  - Disliked Ingredients:', updatedAkMember.dietaryPreferences?.dislikedIngredients);
    
    const isUpdateSuccessful = JSON.stringify(updatedAkMember.dietaryPreferences?.dislikedIngredients) === JSON.stringify(newIngredients);
    
    if (isUpdateSuccessful) {
      console.log('✅ Update successful! Database now contains:', newIngredients);
    } else {
      console.log('❌ Update failed!');
      console.log('  Expected:', newIngredients);
      console.log('  Actual:', updatedAkMember.dietaryPreferences?.dislikedIngredients);
    }
    
    // Test 2: Update to multiple ingredients
    console.log('\n🔄 Test 2: Updating to multiple ingredients...');
    const multipleIngredients = ['daal', 'onions', 'garlic'];
    updatedAkMember.dietaryPreferences.dislikedIngredients = multipleIngredients;
    await updatedUser.save();
    
    // Verify
    const finalUser = await User.findById(user._id);
    const finalAkMember = finalUser.familyMembers.find(member => 
      member.name.toLowerCase() === 'ak'
    );
    
    console.log('🔍 Final verification:');
    console.log('  - Disliked Ingredients:', finalAkMember.dietaryPreferences?.dislikedIngredients);
    
    // Restore original ingredients
    console.log('\n🔄 Restoring original ingredients...');
    finalAkMember.dietaryPreferences.dislikedIngredients = originalIngredients;
    await finalUser.save();
    
    console.log('✅ Original ingredients restored:', originalIngredients);
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    mongoose.connection.close();
  }
}

testAkUpdate();
