import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  Modal,
  TextInput,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Picker } from '@react-native-picker/picker';
import { colors, fonts, spacing, borderRadius } from '../../styles/theme';
import { commonStyles } from '../../styles/commonStyles';

// Dietary preferences information (same as website)
const dietaryPreferencesInfo = [
  { name: 'Vegetarian', description: 'Excludes meat, poultry, and fish but includes dairy and eggs.' },
  { name: 'Vegan', description: 'Excludes all animal products including meat, dairy, eggs, and honey.' },
  { name: 'Gluten-Free', description: 'Excludes wheat, barley, rye, and other gluten-containing grains.' },
  { name: 'Dairy-Free', description: 'Excludes milk and all dairy products like cheese, butter, and yogurt.' },
  { name: 'Nut-Free', description: 'Excludes all tree nuts and peanuts to prevent allergic reactions.' },
  { name: 'Low-Carb', description: 'Limits carbohydrates, focusing on proteins and healthy fats.' },
  { name: 'Keto', description: 'Very low-carb, high-fat diet that puts your body into ketosis.' },
  { name: 'Paleo', description: 'Based on foods presumed to be eaten by early humans: meat, fish, vegetables, fruits.' },
  { name: 'Mediterranean', description: 'Emphasizes fruits, vegetables, whole grains, legumes, olive oil, and fish.' },
  { name: 'Low-Sodium', description: 'Reduces salt intake to help manage blood pressure and heart health.' },
];

const HelpScreen = () => {
  const [showFeedbackModal, setShowFeedbackModal] = useState(false);
  const [feedbackForm, setFeedbackForm] = useState({
    subject: '',
    message: '',
    category: 'general_feedback',
    rating: 5
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleFeedbackSubmit = async () => {
    if (!feedbackForm.subject.trim() || !feedbackForm.message.trim()) {
      Alert.alert('Error', 'Please fill in all required fields');
      return;
    }

    setIsSubmitting(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      Alert.alert(
        'Success',
        'Thank you for your feedback! We appreciate your input.',
        [
          {
            text: 'OK',
            onPress: () => {
              setShowFeedbackModal(false);
              setFeedbackForm({
                subject: '',
                message: '',
                category: 'general_feedback',
                rating: 5
              });
            }
          }
        ]
      );
    } catch (error) {
      Alert.alert('Error', 'Failed to submit feedback. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderFeedbackModal = () => (
    <Modal
      visible={showFeedbackModal}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={() => !isSubmitting && setShowFeedbackModal(false)}
    >
      <View style={styles.modalContainer}>
        <View style={styles.modalHeader}>
          <TouchableOpacity
            onPress={() => setShowFeedbackModal(false)}
            disabled={isSubmitting}
          >
            <Ionicons name="close" size={24} color={colors.text} />
          </TouchableOpacity>
          <Text style={styles.modalTitle}>Send Feedback</Text>
          <TouchableOpacity
            onPress={handleFeedbackSubmit}
            disabled={isSubmitting}
            style={[styles.submitButton, isSubmitting && styles.submitButtonDisabled]}
          >
            {isSubmitting ? (
              <ActivityIndicator size="small" color={colors.primary} />
            ) : (
              <Text style={styles.submitButtonText}>Send</Text>
            )}
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.modalContent}>
          <View style={styles.formGroup}>
            <Text style={styles.label}>Category</Text>
            <View style={styles.pickerContainer}>
              <Picker
                selectedValue={feedbackForm.category}
                onValueChange={(value) => setFeedbackForm(prev => ({ ...prev, category: value }))}
                style={styles.picker}
                enabled={!isSubmitting}
              >
                <Picker.Item label="General Feedback" value="general_feedback" />
                <Picker.Item label="Bug Report" value="bug_report" />
                <Picker.Item label="Feature Request" value="feature_request" />
                <Picker.Item label="Technical Issue" value="technical_issue" />
              </Picker>
            </View>
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Subject *</Text>
            <TextInput
              style={styles.input}
              value={feedbackForm.subject}
              onChangeText={(value) => setFeedbackForm(prev => ({ ...prev, subject: value }))}
              placeholder="Brief description of your feedback"
              placeholderTextColor={colors.textSecondary}
              editable={!isSubmitting}
            />
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Message *</Text>
            <TextInput
              style={[styles.input, styles.textArea]}
              value={feedbackForm.message}
              onChangeText={(value) => setFeedbackForm(prev => ({ ...prev, message: value }))}
              placeholder="Please provide detailed feedback..."
              placeholderTextColor={colors.textSecondary}
              multiline
              numberOfLines={6}
              textAlignVertical="top"
              editable={!isSubmitting}
            />
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Rating</Text>
            <View style={styles.ratingContainer}>
              {[1, 2, 3, 4, 5].map((star) => (
                <TouchableOpacity
                  key={star}
                  onPress={() => setFeedbackForm(prev => ({ ...prev, rating: star }))}
                  disabled={isSubmitting}
                >
                  <Ionicons
                    name={star <= feedbackForm.rating ? "star" : "star-outline"}
                    size={30}
                    color={star <= feedbackForm.rating ? "#FFD700" : colors.textSecondary}
                    style={styles.star}
                  />
                </TouchableOpacity>
              ))}
            </View>
          </View>
        </ScrollView>
      </View>
    </Modal>
  );

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.headerTitle}>HELP CENTER</Text>
          <TouchableOpacity
            style={styles.feedbackButton}
            onPress={() => setShowFeedbackModal(true)}
          >
            <Ionicons name="chatbubble-outline" size={20} color={colors.surface} />
            <Text style={styles.feedbackButtonText}>Send Feedback</Text>
          </TouchableOpacity>
        </View>

        {/* Welcome Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Welcome to PanlasApp Help Center</Text>
          <Text style={styles.sectionText}>
            Find answers to common questions and get help with using PanlasApp for your meal planning needs.
          </Text>
        </View>

        {/* Dietary Preferences Guide */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Dietary Preferences Guide</Text>
          <Text style={styles.sectionText}>
            <Text style={styles.boldText}>Not sure what a dietary preference means?</Text> Here's a quick guide to help you choose the best options for your needs:
          </Text>

          <View style={styles.preferencesContainer}>
            {dietaryPreferencesInfo.map((pref, index) => (
              <View key={index} style={styles.preferenceCard}>
                <Text style={styles.preferenceName}>{pref.name}</Text>
                <Text style={styles.preferenceDescription}>{pref.description}</Text>
              </View>
            ))}
          </View>
        </View>

        {/* Getting Started */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Getting Started</Text>

          <View style={styles.helpItem}>
            <Text style={styles.helpItemTitle}>How to create your first meal plan?</Text>
            <Text style={styles.helpItemText}>
              Navigate to the Meal Plan section and start by selecting your preferred meals for each day. You can customize your plan based on your dietary preferences and family size.
            </Text>
          </View>

          <View style={styles.helpItem}>
            <Text style={styles.helpItemTitle}>Setting up your dietary preferences</Text>
            <Text style={styles.helpItemText}>
              Go to your Profile section to set up dietary restrictions, allergies, and nutritional goals. This helps us provide better meal recommendations.
            </Text>
          </View>
        </View>

        {/* Meal Planning Features */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Meal Planning Features</Text>

          <View style={styles.helpItem}>
            <Text style={styles.helpItemTitle}>How to save favorite meals?</Text>
            <Text style={styles.helpItemText}>
              Tap the heart icon on any meal card to add it to your favorites. Access your saved meals from the Favorites section.
            </Text>
          </View>

          <View style={styles.helpItem}>
            <Text style={styles.helpItemTitle}>Family meal planning</Text>
            <Text style={styles.helpItemText}>
              Add family members in the Family section and set individual dietary preferences for each member to create personalized meal plans.
            </Text>
          </View>
        </View>

        {/* Account Management */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Account Management</Text>

          <View style={styles.helpItem}>
            <Text style={styles.helpItemTitle}>How to update your profile?</Text>
            <Text style={styles.helpItemText}>
              Visit the Profile section to update your personal information, dietary preferences, and account settings.
            </Text>
          </View>

          <View style={styles.helpItem}>
            <Text style={styles.helpItemTitle}>Changing your password</Text>
            <Text style={styles.helpItemText}>
              In your Profile section, you'll find an option to change your password securely.
            </Text>
          </View>
        </View>

        {/* Need More Help */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Need More Help?</Text>
          <Text style={styles.sectionText}>
            Can't find what you're looking for? We'd love to hear from you! Use the feedback button above to:
          </Text>

          <View style={styles.bulletList}>
            <View style={styles.bulletItem}>
              <View style={styles.bullet} />
              <Text style={styles.bulletText}>Report bugs or technical issues</Text>
            </View>
            <View style={styles.bulletItem}>
              <View style={styles.bullet} />
              <Text style={styles.bulletText}>Request new features</Text>
            </View>
            <View style={styles.bulletItem}>
              <View style={styles.bullet} />
              <Text style={styles.bulletText}>Share your experience with PanlasApp</Text>
            </View>
            <View style={styles.bulletItem}>
              <View style={styles.bullet} />
              <Text style={styles.bulletText}>Ask questions not covered in this help center</Text>
            </View>
            <View style={styles.bulletItem}>
              <View style={styles.bullet} />
              <Text style={styles.bulletText}>Suggest improvements to our meal recommendations</Text>
            </View>
          </View>
        </View>
      </ScrollView>

      {renderFeedbackModal()}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  content: {
    flex: 1,
  },
  header: {
    backgroundColor: colors.primary,
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.xl,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: fonts.sizes.xxlarge,
    fontWeight: 'bold',
    color: colors.surface,
    marginBottom: spacing.md,
  },
  feedbackButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.secondary,
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderRadius: borderRadius.large,
    gap: spacing.sm,
  },
  feedbackButtonText: {
    fontSize: fonts.sizes.medium,
    fontWeight: '600',
    color: colors.surface,
  },
  section: {
    backgroundColor: colors.surface,
    marginHorizontal: spacing.md,
    marginVertical: spacing.sm,
    padding: spacing.lg,
    borderRadius: borderRadius.large,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  sectionTitle: {
    fontSize: fonts.sizes.large,
    fontWeight: 'bold',
    color: colors.primary,
    marginBottom: spacing.md,
  },
  sectionText: {
    fontSize: fonts.sizes.medium,
    color: colors.text,
    lineHeight: 22,
    marginBottom: spacing.md,
  },
  boldText: {
    fontWeight: 'bold',
  },
  preferencesContainer: {
    gap: spacing.md,
  },
  preferenceCard: {
    backgroundColor: '#fffbe7',
    borderWidth: 1,
    borderColor: '#ffe0b2',
    borderRadius: borderRadius.medium,
    padding: spacing.md,
  },
  preferenceName: {
    fontSize: fonts.sizes.medium,
    fontWeight: 'bold',
    color: colors.primary,
    marginBottom: spacing.xs,
  },
  preferenceDescription: {
    fontSize: fonts.sizes.small,
    color: colors.textSecondary,
    lineHeight: 18,
  },
  helpItem: {
    marginBottom: spacing.lg,
  },
  helpItemTitle: {
    fontSize: fonts.sizes.medium,
    fontWeight: '600',
    color: colors.text,
    marginBottom: spacing.sm,
  },
  helpItemText: {
    fontSize: fonts.sizes.medium,
    color: colors.textSecondary,
    lineHeight: 20,
  },
  bulletList: {
    marginTop: spacing.md,
  },
  bulletItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: spacing.sm,
  },
  bullet: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: colors.primary,
    marginTop: 8,
    marginRight: spacing.md,
  },
  bulletText: {
    flex: 1,
    fontSize: fonts.sizes.medium,
    color: colors.textSecondary,
    lineHeight: 20,
  },
  // Modal styles
  modalContainer: {
    flex: 1,
    backgroundColor: colors.background,
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
    backgroundColor: colors.surface,
  },
  modalTitle: {
    fontSize: fonts.sizes.large,
    fontWeight: '600',
    color: colors.text,
  },
  submitButton: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
  },
  submitButtonDisabled: {
    opacity: 0.5,
  },
  submitButtonText: {
    fontSize: fonts.sizes.medium,
    fontWeight: '600',
    color: colors.primary,
  },
  modalContent: {
    flex: 1,
    paddingHorizontal: spacing.lg,
    paddingTop: spacing.lg,
  },
  formGroup: {
    marginBottom: spacing.lg,
  },
  label: {
    fontSize: fonts.sizes.medium,
    fontWeight: '500',
    color: colors.text,
    marginBottom: spacing.sm,
  },
  input: {
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: borderRadius.medium,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.md,
    fontSize: fonts.sizes.medium,
    color: colors.text,
    backgroundColor: colors.surface,
  },
  textArea: {
    height: 120,
    textAlignVertical: 'top',
  },
  pickerContainer: {
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: borderRadius.medium,
    backgroundColor: colors.surface,
  },
  picker: {
    height: 50,
    color: colors.text,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.sm,
  },
  star: {
    marginHorizontal: 2,
  },
});

export default HelpScreen;
