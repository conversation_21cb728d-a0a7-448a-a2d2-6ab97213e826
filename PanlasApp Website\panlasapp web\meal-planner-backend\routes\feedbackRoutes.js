const express = require('express');
const router = express.Router();
const feedbackController = require('../controllers/feedbackController');
const auth = require('../middleware/auth');
const { adminOrSubAdminAuth, canManageFeedback } = require('../middleware/adminAuth');

console.log('=== FEEDBACK ROUTES FILE LOADED ===');

// Test route (no auth required)
router.get('/test', (req, res) => {
  console.log('=== FEEDBACK TEST ROUTE HIT ===');
  res.json({ message: 'Feedback routes are working!' });
});

// Apply auth middleware to all routes below
router.use(auth);

// User routes
router.post('/submit', (req, res, next) => {
  console.log('=== FEEDBACK SUBMIT ROUTE HIT ===');
  console.log('Method:', req.method);
  console.log('URL:', req.url);
  console.log('Body:', req.body);
  next();
}, feedbackController.submitFeedback);

// Admin routes (admin or sub admin with feedback management permission)
router.get('/all', adminOrSubAdminAuth, canManageFeedback, feedbackController.getAllFeedback);
router.put('/:feedbackId/status', adminOrSubAdminAuth, canManageFeedback, feedbackController.updateFeedbackStatus);
router.post('/:feedbackId/response', adminOrSubAdminAuth, canManageFeedback, feedbackController.addAdminResponse);
router.delete('/:feedbackId', adminOrSubAdminAuth, canManageFeedback, feedbackController.deleteFeedback);

console.log('=== FEEDBACK ROUTES SETUP COMPLETE ===');

module.exports = router;
