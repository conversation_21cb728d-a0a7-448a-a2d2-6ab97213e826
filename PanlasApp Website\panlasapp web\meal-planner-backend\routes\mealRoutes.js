const express = require('express');
const router = express.Router();
const mealController = require('../controllers/mealController');
const auth = require('../middleware/auth');
const { optionalAuth } = require('../middleware/auth'); // Add auth middleware
const { adminOrSubAdminAuth, canManageMeals } = require('../middleware/adminAuth');

// Get all meals
router.get('/', mealController.getMeals);

// Get a single meal
router.get('/meals:id', mealController.getFilipinoDishById);

// Create a new meal (admin or sub admin with meal management permission)
router.post('/', auth, adminOrSubAdminAuth, canManageMeals, mealController.createMeal);

// Update a meal (admin or sub admin with meal management permission)
router.put('/:id', auth, adminOrSubAdminAuth, canManageMeals, mealController.updateMeal);

// Delete a meal (admin or sub admin with meal management permission)
router.delete('/:id', auth, adminOrSubAdminAuth, canManageMeals, mealController.deleteMeal);

// New routes for dietary preferences
router.get('/search/filters', mealController.searchMeals);
router.get('/dietary/preferences', auth, mealController.getMealsByDietaryPreferences);
router.get('/suggestions/type', optionalAuth, mealController.getMealSuggestions);
router.get('/recommendations', auth, mealController.getMealRecommendations);
router.get('/popular/list', optionalAuth, mealController.getPopularMeals);

// New routes for Filipino dishes
router.get('/filipino', mealController.getFilipinoDishes);
router.get('/filipino/:id', mealController.getFilipinoDishById);

module.exports = router;
