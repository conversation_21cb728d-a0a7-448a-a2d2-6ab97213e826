const mongoose = require('mongoose');
const User = require('../models/User');
const Meal = require('../models/Meal');
const geminiService = require('../services/geminiService');
require('dotenv').config();

async function testFamilyMealPlan() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ Connected to MongoDB');

    // Get some meals from database
    const allMeals = await Meal.find().limit(20);
    console.log(`📊 Found ${allMeals.length} meals in database`);

    // Test family profile with conflicts
    const testFamilyProfile = {
      dietaryPreferences: {
        restrictions: ['Vegetarian'],
        allergies: ['Nuts'],
        dislikedIngredients: ['Mushrooms'],
        calorieTarget: 2000,
        mealFrequency: 3
      },
      familyMembers: [
        {
          name: '<PERSON>',
          dateOfBirth: new Date('1985-05-15'),
          dietaryPreferences: {
            restrictions: ['Vegan'], // Conflict with user's Vegetarian
            allergies: ['Milk'],
            calorieTarget: 1800
          }
        },
        {
          name: '<PERSON>',
          dateOfBirth: new Date('2010-03-20'),
          dietaryPreferences: {
            restrictions: ['Gluten-Free'],
            allergies: ['Eggs'],
            calorieTarget: 1600
          }
        }
      ]
    };

    console.log('\n🔍 Testing family preference aggregation...');
    const aggregationResult = geminiService.aggregateFamilyPreferences(testFamilyProfile);
    
    console.log('Aggregated Preferences:', aggregationResult.aggregatedPreferences);
    console.log('Detected Conflicts:', aggregationResult.conflicts);
    console.log('Family Size:', aggregationResult.familySize);

    console.log('\n🍽️ Testing family meal plan generation...');
    const familyMealPlan = await geminiService.generateFamilyMealPlan(
      testFamilyProfile,
      allMeals
    );

    console.log('\n📋 Generated Family Meal Plan:');
    console.log('Personalized Message:', familyMealPlan.personalizedMessage);
    console.log('Nutritional Summary:', familyMealPlan.nutritionalSummary);
    console.log('Conflicts:', familyMealPlan.conflicts);
    
    if (familyMealPlan.mealPlan.breakfast) {
      console.log('\n🌅 Breakfast:');
      familyMealPlan.mealPlan.breakfast.forEach(meal => {
        console.log(`  • ${meal.mealName}: ${meal.reason}`);
      });
    }

    if (familyMealPlan.mealPlan.lunch) {
      console.log('\n☀️ Lunch:');
      familyMealPlan.mealPlan.lunch.forEach(meal => {
        console.log(`  • ${meal.mealName}: ${meal.reason}`);
      });
    }

    if (familyMealPlan.mealPlan.dinner) {
      console.log('\n🌙 Dinner:');
      familyMealPlan.mealPlan.dinner.forEach(meal => {
        console.log(`  • ${meal.mealName}: ${meal.reason}`);
      });
    }

    await mongoose.disconnect();
    console.log('\n🔌 Disconnected from MongoDB');
    console.log('\n🎉 Family meal plan testing completed!');

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

testFamilyMealPlan();
