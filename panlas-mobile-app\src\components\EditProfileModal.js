import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Picker } from '@react-native-picker/picker';
import { colors, fonts, spacing, borderRadius } from '../styles/theme';
import { userAPI } from '../services/api';

const EditProfileModal = ({ visible, onClose, user, onProfileUpdate }) => {
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    dateOfBirth: '',
    gender: '',
    barangay: ''
  });
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});

  useEffect(() => {
    if (user && visible) {
      try {
        console.log('Setting up EditProfileModal with user data:', user);

        // Safely handle dateOfBirth conversion
        let formattedDateOfBirth = '';
        if (user.dateOfBirth) {
          try {
            // Handle both string and Date object types
            const dateStr = typeof user.dateOfBirth === 'string'
              ? user.dateOfBirth
              : user.dateOfBirth.toString();
            formattedDateOfBirth = dateStr.split('T')[0];
          } catch (error) {
            console.warn('Error formatting dateOfBirth:', error);
            formattedDateOfBirth = '';
          }
        }

        setFormData({
          firstName: String(user.firstName || ''),
          lastName: String(user.lastName || ''),
          dateOfBirth: formattedDateOfBirth,
          gender: String(user.gender || ''),
          barangay: String(user.barangay || '')
        });
        setErrors({});
      } catch (error) {
        console.error('Error setting up EditProfileModal:', error);
        // Set default empty form data if there's an error
        setFormData({
          firstName: '',
          lastName: '',
          dateOfBirth: '',
          gender: '',
          barangay: ''
        });
        setErrors({});
      }
    }
  }, [user, visible]);

  const handleInputChange = (field, value) => {
    // Ensure value is always a string to prevent type errors
    const stringValue = value != null ? String(value) : '';

    setFormData(prev => ({
      ...prev,
      [field]: stringValue
    }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: null
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    // Safely check firstName with type conversion
    const firstName = String(formData.firstName || '');
    if (!firstName.trim()) {
      newErrors.firstName = 'First name is required';
    }

    // Safely check lastName with type conversion
    const lastName = String(formData.lastName || '');
    if (!lastName.trim()) {
      newErrors.lastName = 'Last name is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    try {
      // Ensure all form data is properly formatted for API
      const sanitizedFormData = {
        firstName: String(formData.firstName || '').trim(),
        lastName: String(formData.lastName || '').trim(),
        dateOfBirth: String(formData.dateOfBirth || '').trim(),
        gender: String(formData.gender || '').trim(),
        barangay: String(formData.barangay || '').trim()
      };

      console.log('Submitting profile data:', sanitizedFormData);
      const response = await userAPI.updateProfile(sanitizedFormData);

      Alert.alert(
        'Success',
        'Profile updated successfully!',
        [
          {
            text: 'OK',
            onPress: () => {
              if (response?.data?.user) {
                onProfileUpdate(response.data.user);
              }
              onClose();
            }
          }
        ]
      );
    } catch (error) {
      console.error('Profile update error:', error);
      Alert.alert(
        'Error',
        error.response?.data?.message || 'Failed to update profile'
      );
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    if (!loading) {
      onClose();
    }
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={handleClose}
    >
      <View style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity onPress={handleClose} disabled={loading}>
            <Ionicons name="close" size={24} color={colors.text} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Edit Profile</Text>
          <TouchableOpacity 
            onPress={handleSubmit} 
            disabled={loading}
            style={[styles.saveButton, loading && styles.saveButtonDisabled]}
          >
            {loading ? (
              <ActivityIndicator size="small" color={colors.primary} />
            ) : (
              <Text style={styles.saveButtonText}>Save</Text>
            )}
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* First Name */}
          <View style={styles.formGroup}>
            <Text style={styles.label}>
              <Ionicons name="person-outline" size={16} color={colors.primary} /> First Name *
            </Text>
            <TextInput
              style={[styles.input, errors.firstName && styles.inputError]}
              value={formData.firstName}
              onChangeText={(value) => handleInputChange('firstName', value)}
              placeholder="Enter your first name"
              placeholderTextColor={colors.textSecondary}
              editable={!loading}
            />
            {errors.firstName && (
              <Text style={styles.errorText}>{errors.firstName}</Text>
            )}
          </View>

          {/* Last Name */}
          <View style={styles.formGroup}>
            <Text style={styles.label}>
              <Ionicons name="person-outline" size={16} color={colors.primary} /> Last Name *
            </Text>
            <TextInput
              style={[styles.input, errors.lastName && styles.inputError]}
              value={formData.lastName}
              onChangeText={(value) => handleInputChange('lastName', value)}
              placeholder="Enter your last name"
              placeholderTextColor={colors.textSecondary}
              editable={!loading}
            />
            {errors.lastName && (
              <Text style={styles.errorText}>{errors.lastName}</Text>
            )}
          </View>

          {/* Date of Birth */}
          <View style={styles.formGroup}>
            <Text style={styles.label}>
              <Ionicons name="calendar-outline" size={16} color={colors.primary} /> Date of Birth
            </Text>
            <TextInput
              style={styles.input}
              value={formData.dateOfBirth}
              onChangeText={(value) => handleInputChange('dateOfBirth', value)}
              placeholder="YYYY-MM-DD"
              placeholderTextColor={colors.textSecondary}
              editable={!loading}
            />
          </View>

          {/* Gender */}
          <View style={styles.formGroup}>
            <Text style={styles.label}>
              <Ionicons name="male-female-outline" size={16} color={colors.primary} /> Gender
            </Text>
            <View style={styles.pickerContainer}>
              <Picker
                selectedValue={formData.gender}
                onValueChange={(value) => handleInputChange('gender', value)}
                style={styles.picker}
                enabled={!loading}
              >
                <Picker.Item label="Select gender" value="" />
                <Picker.Item label="Male" value="male" />
                <Picker.Item label="Female" value="female" />
                <Picker.Item label="Other" value="other" />
                <Picker.Item label="Prefer not to say" value="prefer not to say" />
              </Picker>
            </View>
          </View>

          {/* Barangay */}
          <View style={styles.formGroup}>
            <Text style={styles.label}>
              <Ionicons name="location-outline" size={16} color={colors.primary} /> Barangay
            </Text>
            <TextInput
              style={styles.input}
              value={formData.barangay}
              onChangeText={(value) => handleInputChange('barangay', value)}
              placeholder="Enter your barangay"
              placeholderTextColor={colors.textSecondary}
              editable={!loading}
            />
          </View>
        </ScrollView>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
    backgroundColor: colors.surface,
  },
  headerTitle: {
    fontSize: fonts.sizes.large,
    fontWeight: '600',
    color: colors.text,
  },
  saveButton: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
  },
  saveButtonDisabled: {
    opacity: 0.5,
  },
  saveButtonText: {
    fontSize: fonts.sizes.medium,
    fontWeight: '600',
    color: colors.primary,
  },
  content: {
    flex: 1,
    paddingHorizontal: spacing.lg,
    paddingTop: spacing.lg,
  },
  formGroup: {
    marginBottom: spacing.lg,
  },
  label: {
    fontSize: fonts.sizes.medium,
    fontWeight: '500',
    color: colors.text,
    marginBottom: spacing.sm,
  },
  input: {
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: borderRadius.medium,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.md,
    fontSize: fonts.sizes.medium,
    color: colors.text,
    backgroundColor: colors.surface,
  },
  inputError: {
    borderColor: colors.error,
  },
  errorText: {
    fontSize: fonts.sizes.small,
    color: colors.error,
    marginTop: spacing.xs,
  },
  pickerContainer: {
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: borderRadius.medium,
    backgroundColor: colors.surface,
  },
  picker: {
    height: 50,
    color: colors.text,
  },
});

export default EditProfileModal;
