const Activity = require('../models/Activity');
const { getClientIP } = require('./analytics');

// Helper function to log activity
const logActivity = async (userId, action, details = {}, ipAddress = null) => {
  try {
    if (!userId) return;
    
    const activity = new Activity({
      user: userId,
      action,
      details,
      ipAddress
    });

    await activity.save();
    console.log(`✅ Activity logged: ${action} for user ${userId}`);
  } catch (error) {
    console.error('❌ Error logging activity:', error);
  }
};

// Middleware to automatically log certain activities
const activityLogger = (action, getDetails = null) => {
  return async (req, res, next) => {
    // Store original res.json to intercept successful responses
    const originalJson = res.json;
    
    res.json = function(data) {
      // Only log on successful responses (status < 400)
      if (res.statusCode < 400 && req.user && req.user.id) {
        const ipAddress = getClientIP(req);
        let details = {};
        
        // Get details from custom function if provided
        if (getDetails && typeof getDetails === 'function') {
          details = getDetails(req, res, data);
        }
        
        // Log activity asynchronously
        setImmediate(() => {
          logActivity(req.user.id, action, details, ipAddress);
        });
      }
      
      // Call original json method
      return originalJson.call(this, data);
    };
    
    next();
  };
};

// Specific activity loggers for common actions
const loginLogger = activityLogger('login', (req, res, data) => ({
  email: req.body.email,
  loginTime: new Date(),
  userAgent: req.headers['user-agent'],
  success: true
}));

const logoutLogger = activityLogger('logout', (req, res, data) => ({
  logoutTime: new Date(),
  sessionDuration: req.sessionDuration || null
}));

const profileUpdateLogger = activityLogger('update_profile', (req, res, data) => ({
  updatedFields: req.body,
  updateTime: new Date()
}));

const mealPlanCreateLogger = activityLogger('create_meal_plan', (req, res, data) => ({
  date: req.body.date,
  mealType: req.body.mealType,
  mealName: req.body.meal?.name || 'Unknown meal',
  createTime: new Date()
}));

const mealPlanUpdateLogger = activityLogger('update_meal_plan', (req, res, data) => ({
  date: req.body.date,
  mealType: req.body.mealType,
  mealName: req.body.meal?.name || 'Unknown meal',
  updateTime: new Date()
}));

const mealPlanDeleteLogger = activityLogger('delete_meal_plan', (req, res, data) => ({
  date: req.params.date,
  deleteTime: new Date()
}));

const mealCreateLogger = activityLogger('create_meal', (req, res, data) => ({
  mealName: req.body.name,
  category: req.body.category,
  calories: req.body.calories,
  createTime: new Date()
}));

module.exports = {
  logActivity,
  activityLogger,
  loginLogger,
  logoutLogger,
  profileUpdateLogger,
  mealPlanCreateLogger,
  mealPlanUpdateLogger,
  mealPlanDeleteLogger,
  mealCreateLogger
};
