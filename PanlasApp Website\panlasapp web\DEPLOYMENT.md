# PanlasApp Deployment Guide

This guide will help you deploy the PanlasApp frontend to Vercel and the backend to Railway.

## Prerequisites

- Node.js 18+ installed
- Git repository set up
- Vercel account (free tier available)
- Railway account (free tier available)
- MongoDB database (MongoDB Atlas recommended for production)

## Backend Deployment (Railway)

### 1. Prepare Your Backend

1. Navigate to the backend directory:
   ```bash
   cd "PanlasApp Website/panlasapp web/meal-planner-backend"
   ```

2. Create a `.env` file from the template:
   ```bash
   cp .env.example .env
   ```

3. Update the `.env` file with your production values:
   - `MONGODB_URI`: Your MongoDB connection string
   - `JWT_SECRET`: A strong, random secret key
   - `FRONTEND_URL`: Your Vercel domain (will be updated after frontend deployment)
   - `EMAIL_USER` and `EMAIL_PASS`: For password reset functionality
   - Other variables as needed

### 2. Deploy to Railway

1. Go to [Railway.app](https://railway.app) and sign in
2. Click "New Project" → "Deploy from GitHub repo"
3. Connect your GitHub account and select your repository
4. Choose the backend directory: `PanlasApp Website/panlasapp web/meal-planner-backend`
5. Railway will automatically detect it's a Node.js project

### 3. Configure Environment Variables

In your Railway project dashboard:
1. Go to "Variables" tab
2. Add all the environment variables from your `.env` file
3. Make sure to set `NODE_ENV=production`

### 4. Configure Custom Domain (Optional)

1. In Railway dashboard, go to "Settings" → "Domains"
2. Add a custom domain or use the provided Railway domain
3. Note this URL for frontend configuration

## Frontend Deployment (Vercel)

### 1. Prepare Your Frontend

1. Navigate to the frontend directory:
   ```bash
   cd "PanlasApp Website/panlasapp web"
   ```

2. Create a `.env.local` file:
   ```bash
   cp .env.example .env.local
   ```

3. Update the `.env.local` file:
   ```
   VITE_API_URL=https://your-railway-backend-url.railway.app/api
   ```

### 2. Deploy to Vercel

1. Go to [Vercel.com](https://vercel.com) and sign in
2. Click "New Project"
3. Import your Git repository
4. Set the root directory to: `PanlasApp Website/panlasapp web`
5. Vercel will automatically detect it's a Vite project

### 3. Configure Environment Variables

In your Vercel project dashboard:
1. Go to "Settings" → "Environment Variables"
2. Add: `VITE_API_URL` with your Railway backend URL
3. Add any other environment variables from `.env.example`

### 4. Configure Build Settings

Vercel should automatically detect the correct settings, but verify:
- Build Command: `npm run build`
- Output Directory: `dist`
- Install Command: `npm install`

## Post-Deployment Configuration

### 1. Update CORS Settings

Update your backend's environment variables on Railway:
- `FRONTEND_URL`: Your Vercel domain (e.g., `https://your-app.vercel.app`)
- `CORS_ORIGIN`: Same as FRONTEND_URL

### 2. Test the Deployment

1. Visit your Vercel frontend URL
2. Test user registration and login
3. Test API endpoints
4. Check browser console for any errors

### 3. Set up Database

If using a new MongoDB database:
1. Run the seed script to populate initial data:
   ```bash
   npm run seed
   ```
2. Create an admin user:
   ```bash
   npm run make-admin
   ```

## Troubleshooting

### Common Issues

1. **CORS Errors**: Ensure `FRONTEND_URL` and `CORS_ORIGIN` are correctly set in Railway
2. **API Connection Issues**: Verify `VITE_API_URL` in Vercel environment variables
3. **Database Connection**: Check MongoDB URI and network access settings
4. **Build Failures**: Check Node.js version compatibility

### Logs

- **Railway**: Check logs in the Railway dashboard under "Deployments"
- **Vercel**: Check function logs in Vercel dashboard under "Functions"

## Environment Variables Reference

### Frontend (.env.local)
```
VITE_API_URL=https://your-backend.railway.app/api
```

### Backend (Railway Environment Variables)
```
NODE_ENV=production
MONGODB_URI=mongodb+srv://...
JWT_SECRET=your-secret-key
FRONTEND_URL=https://your-app.vercel.app
CORS_ORIGIN=https://your-app.vercel.app
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password
```

## Support

If you encounter issues:
1. Check the logs in both Vercel and Railway dashboards
2. Verify all environment variables are set correctly
3. Ensure your MongoDB database is accessible
4. Test API endpoints directly using tools like Postman

## Security Notes

- Never commit `.env` files to version control
- Use strong, unique passwords and secrets
- Enable 2FA on your deployment platforms
- Regularly update dependencies
- Monitor your applications for security vulnerabilities
