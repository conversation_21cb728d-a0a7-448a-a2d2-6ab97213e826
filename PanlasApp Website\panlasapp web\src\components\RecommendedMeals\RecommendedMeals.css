/* Recommended Meals Component Styles */
.recommended-meals-container {
  width: 100%;
  max-width: 100%;
  margin-left: 0;
  margin-right: 0;
  border-radius: 0;
  padding-left: 0;
  padding-right: 0;
}

.recommended-meals-header {
  margin-bottom: 24px;
}

.header-content h2 {
  color: #2c3e50;
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
}


.header-content p {
  color: #78909c;
  font-size: 14px;
  margin-bottom: 16px;
}

.applied-filters {
  background: rgba(248, 249, 250, 0.9);
  border: 1px solid #e1e8ed;
  border-radius: 12px;
  padding: 16px;
  margin-top: 12px;
}

.filters-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.filters-title {
  font-size: 14px;
  font-weight: 600;
  color: #2c3e50;
  display: flex;
  align-items: center;
  gap: 6px;
}

.family-indicator {
  font-size: 12px;
  color: #78909c;
  background: #e8f5e8;
  padding: 2px 8px;
  border-radius: 10px;
}

.filters-content {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.filter-group {
  display: flex;
  align-items: center;
}

.filter-tag {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 4px;
  border: 1px solid transparent;
}

.filter-tag.dietary {
  background:  #70e4c4;
  color: white;
}

.filter-tag.calorie {
  background: linear-gradient(135deg, #74b9ff, #0984e3);
  color: white;
}

.filter-tag.allergy {
  background: linear-gradient(135deg, #ff6b6b, #ee5a52);
  color: white;
}

/* --- Horizontal Scroll Layout for Recommendations --- */
.recommendations-grid {
  display: flex !important;
  flex-direction: row !important;
  overflow-x: auto !important;
  overflow-y: visible !important;
  gap: 20px !important;
  padding-bottom: 8px;
  scroll-snap-type: x mandatory;
  -webkit-overflow-scrolling: touch;
  align-items: stretch !important;
  margin-bottom: 24px;
  animation: fadeInUp 0.6s ease-out;
}

/* Hide scrollbar for webkit browsers */
/* .recommendations-grid::-webkit-scrollbar {
  display: none;
} */

/* Hide scrollbar for Firefox */
/* .recommendations-grid {
  scrollbar-width: none;
} */

/* --- Recommendation Card --- */
.recommendation-card {
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  position: relative;
  height: 100%;
  min-width: 320px;
  max-width: 340px;
  flex: 0 0 auto;
  min-height: 480px;
  animation: slideInUp 0.6s ease-out both;
  border: 1px solid #f1f5f9;
  overflow: hidden;
  scroll-snap-align: start;
}

.recommendation-card:hover {
  transform: translateY(-6px);
  box-shadow: 0 12px 28px rgba(0, 0, 0, 0.15);
  border-color: #70e4c4;
}

.recommendation-image {
  position: relative;
  height: 200px;
  width: 100%;
  overflow: hidden;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  flex-shrink: 0;
}

.recommendation-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  transition: transform 0.4s ease;
  display: block;
}

.recommendation-card:hover .recommendation-image img {
  transform: scale(1.08);
}

.meal-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 48px;
  color: #adb5bd;
  border-radius: 0;
}

.recommendation-image .favorite-btn {
  background-color: rgba(255, 255, 255, 0.9);
  border: none;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
  position: absolute;
  top: 12px;
  right: 12px;
}

.recommendation-image .favorite-btn:hover {
  transform: scale(1.1);
  background-color: white;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2);
}

.recommendation-image .favorite-btn.favorited {
  color: #e74c3c;
}

.recommendation-score {
  position: absolute;
  top: 12px;
  left: 12px;
  background: #ffc107;
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 4px;
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 8px rgba(255, 193, 7, 0.3);
}

.recommendation-content {
  padding: 20px;
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  gap: 14px;
  justify-content: space-between;
  min-height: 280px;
}

.meal-name {
  font-size: 18px;
  font-weight: 700;
  color: #2c3e50;
  line-height: 1.3;
  margin: 0;
  min-height: 48px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.meal-stats {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
  font-size: 13px;
  color: #78909c;
  background: rgba(248, 249, 250, 0.9);
  padding: 10px 14px;
  border-radius: 10px;
  margin: 0;
  border: 1px solid #f1f5f9;
}

.meal-stats span {
  display: flex;
  align-items: center;
  gap: 4px;
  font-weight: 600;
  white-space: nowrap;
}

.calories {
  color: #ff6b6b;
}

.prep-time {
  color: #4ecdc4;
}

.rating {
  color: #ffc107;
}

.dietary-badges {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  margin: 0;
  min-height: 28px;
  align-items: flex-start;
}

.dietary-badge {
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 3px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  white-space: nowrap;
}

.meal-description {
  color: #78909c;
  font-size: 14px;
  line-height: 1.5;
  min-height: 42px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.recommendation-reason {
  background: rgba(112, 228, 196, 0.08);
  border-left: 3px solid #70e4c4;
  padding: 12px 16px;
  border-radius: 0 8px 8px 0;
  margin-top: auto;
  margin-bottom: 16px;
}

.reason-text {
  font-size: 12px;
  color: #2c3e50;
  font-weight: 600;
  font-style: italic;
  line-height: 1.4;
}

.view-meal-btn {
  width: 100%;
  background: linear-gradient(135deg, #70e4c4, #2bdfac);
  color: white;
  border: none;
  padding: 14px 20px;
  border-radius: 10px;
  font-size: 14px;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-top: auto;
  min-height: 48px;
  box-shadow: 0 2px 8px rgba(112, 228, 196, 0.2);
}

.view-meal-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(112, 228, 196, 0.4);
  background: linear-gradient(135deg, #5dd4b4, #1ecf9c);
}

.view-meal-btn:active {
  transform: translateY(0);
}

.recommendations-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 60px 20px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(112, 228, 196, 0.2);
  border-radius: 50%;
  border-top-color: #70e4c4;
  animation: circleSpinner 1s ease-in-out infinite;
}

/* Use consistent circle loader */
.recommendations-circle-loader {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(112, 228, 196, 0.2);
  border-radius: 50%;
  border-top-color: #70e4c4;
  animation: circleSpinner 1s ease-in-out infinite;
}

@keyframes circleSpinner {
  to { transform: rotate(360deg); }
}

.recommendations-footer {
  text-align: center;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.refresh-recommendations-btn {
  background: rgba(112, 228, 196, 0.1);
  color: #70e4c4;
  border: 2px solid #70e4c4;
  padding: 10px 20px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.refresh-recommendations-btn:hover:not(:disabled) {
  background: #70e4c4;
  color: white;
}

.refresh-recommendations-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.error-text {
  color: #e74c3c;
  font-style: italic;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .recommendation-card {
    min-width: 280px;
    max-width: 300px;
    min-height: 460px;
  }
}

@media (max-width: 1024px) {
  .recommendation-image {
    height: 180px;
  }
  .recommendation-card {
    min-width: 260px;
    max-width: 280px;
    min-height: 440px;
  }
}

@media (max-width: 768px) {
  .recommended-meals-container {
    margin: 16px 0;
    padding: 18px;
  }
  .recommendation-image {
    height: 180px;
  }
  .header-content h2 {
    font-size: 22px;
  }
  .applied-filters {
    flex-direction: row;
    flex-wrap: wrap;
    gap: 8px;
  }
  .recommendation-content {
    padding: 16px;
    min-height: 260px;
  }
  .recommendation-card {
    min-width: 80vw;
    max-width: 90vw;
    min-height: 420px;
  }
}

@media (max-width: 480px) {
  .recommended-meals-container {
    margin: 12px 0;
    padding: 16px;
  }
  .recommendation-image {
    height: 160px;
  }
  .meal-stats {
    flex-direction: row;
    flex-wrap: wrap;
    align-items: center;
    gap: 6px;
    padding: 8px 12px;
  }
  .dietary-badges {
    justify-content: flex-start;
    gap: 4px;
  }
  .meal-name {
    font-size: 16px;
    min-height: 40px;
  }
  .view-meal-btn {
    padding: 12px 16px;
    font-size: 13px;
    min-height: 44px;
  }
  .favorite-btn, .recommendation-score {
    top: 10px;
  }
  .favorite-btn {
    right: 10px;
    width: 32px;
    height: 32px;
  }
  .recommendation-score {
    left: 10px;
    padding: 3px 6px;
    font-size: 10px;
  }
  .recommendation-content {
    padding: 14px;
    min-height: 240px;
  }
  .recommendation-card {
    min-width: 92vw;
    max-width: 98vw;
    min-height: 380px;
  }
}

/* Override any conflicting styles from App.css - ONLY for main homepage recommendations */
.recommended-meals-container .recommendations-grid .recommendation-card {
  background-color: white !important;
  border-radius: 12px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08) !important;
  transition: all 0.3s ease !important;
  display: flex !important;
  flex-direction: column !important;
  position: relative !important;
  height: 100% !important;
  border: 1px solid #f1f5f9 !important;
  overflow: hidden !important;
}

.recommended-meals-container .recommendations-grid .recommendation-image {
  position: relative !important;
  height: 200px !important;
  width: 100% !important;
  overflow: hidden !important;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef) !important;
  flex-shrink: 0 !important;
}

.recommended-meals-container .recommendations-grid .recommendation-image img {
  width: 100% !important;
  height: 100% !important;
  object-fit: cover !important;
  object-position: center !important;
  transition: transform 0.4s ease !important;
  display: block !important;
}

.recommended-meals-container .recommendations-grid .recommendation-content {
  padding: 20px !important;
  display: flex !important;
  flex-direction: column !important;
  flex-grow: 1 !important;
  gap: 14px !important;
  justify-content: space-between !important;
}

/* Ensure sidebar recommendations are NOT affected by these styles */
.sidebar .recommendation-item {
  display: flex !important;
  gap: 0.75rem !important;
  padding: 0.75rem !important;
  background: #f8f9fa !important;
  border-radius: 8px !important;
  transition: all 0.2s ease !important;
  cursor: pointer !important;
  border: 1px solid transparent !important;
}

.sidebar .recommendation-image {
  width: 50px !important;
  height: 50px !important;
  border-radius: 6px !important;
  overflow: hidden !important;
  flex-shrink: 0 !important;
  background: #f8f9fa !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.sidebar .recommendation-image img {
  width: 100% !important;
  height: 100% !important;
  object-fit: cover !important;
}

.sidebar .recommendation-content {
  flex: 1 !important;
  min-width: 0 !important;
}

.pagination-controls {
  display: flex;
  justify-content: center;
  gap: 8px;
  margin: 24px 0;
}
.pagination-btn {
  background: #f1f5f9;
  border: none;
  border-radius: 6px;
  padding: 8px 14px;
  font-size: 16px;
  cursor: pointer;
  color: #2c3e50;
  transition: background 0.2s;
}
.pagination-btn.active,
.pagination-btn:hover {
  background: linear-gradient(135deg, #70e4c4, #2bdfac);
  color: #fff;
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(40px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}
