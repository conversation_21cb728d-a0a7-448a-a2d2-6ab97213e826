const axios = require('axios');

const API_BASE_URL = 'http://localhost:5000/api';

// Test data
const testEditRequest = {
  currentMealPlan: {
    breakfast: [
      {
        name: "<PERSON><PERSON><PERSON>",
        price: 250,
        description: "Classic Filipino chicken adobo",
        calories: 320,
        protein: 28,
        carbs: 8,
        fat: 18
      }
    ],
    lunch: [
      {
        name: "<PERSON><PERSON><PERSON><PERSON><PERSON>",
        price: 300,
        description: "Filipino oxtail stew with peanut sauce",
        calories: 450,
        protein: 25,
        carbs: 35,
        fat: 22
      }
    ],
    dinner: [
      {
        name: "Sinigang na Baboy",
        price: 280,
        description: "Pork sour soup with vegetables",
        calories: 380,
        protein: 30,
        carbs: 20,
        fat: 18
      }
    ]
  },
  editRequest: "Replace <PERSON>re<PERSON><PERSON>re with <PERSON><PERSON> in lunch",
  isFamily: false
};

async function testEditMealPlan() {
  try {
    console.log('🧪 Testing Edit Meal Plan API...');
    console.log('📝 Edit Request:', testEditRequest.editRequest);
    
    const response = await axios.post(`${API_BASE_URL}/ai/edit-meal-plan`, testEditRequest, {
      headers: {
        'Content-Type': 'application/json'
      }
    });

    if (response.data.success) {
      console.log('✅ Edit meal plan successful!');
      console.log('📋 Updated Meal Plan:');
      console.log(JSON.stringify(response.data.mealPlan, null, 2));
      console.log('\n💬 Personalized Message:');
      console.log(response.data.personalizedMessage);
      console.log('\n🥗 Nutritional Summary:');
      console.log(response.data.nutritionalSummary);
      console.log('\n💰 Total Cost:', response.data.totalCost);
    } else {
      console.log('❌ Edit meal plan failed:', response.data.message);
    }
  } catch (error) {
    console.error('❌ Error testing edit meal plan:', error.response?.data || error.message);
  }
}

// Run the test
testEditMealPlan();
